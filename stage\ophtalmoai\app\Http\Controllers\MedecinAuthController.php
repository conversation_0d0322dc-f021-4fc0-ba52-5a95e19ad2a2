<?php

namespace App\Http\Controllers;
use App\Models\Medecin;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;




use Illuminate\Http\Request;

class MedecinAuthController extends Controller
{
    public function register(Request $request)
{
    $validated = $request->validate([
        'nom' => 'required|string',
        'email' => 'required|email|unique:medecins,email',
        'password' => 'required|string|confirmed',
        'specialite' => 'required|string',
    ]);

    $validated['password'] = bcrypt($validated['password']);

    $medecin = Medecin::create($validated);

    $token = $medecin->createToken('medecin_token')->plainTextToken;

    return response()->json([
        'medecin' => $medecin,
        'token' => $token,
    ], 201);
}


public function login(Request $request)
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required'
    ]);

    $medecin = Medecin::where('email', $request->email)->first();
    Log::info('Medecin trouvé : ', ['medecin' => $medecin]);

    if (!$medecin || !Hash::check($request->password, $medecin->password)) {
        Log::warning('Échec d\'authentification pour : ' . $request->email);
        return response()->json(['message' => 'Email ou mot de passe incorrect'], 401);
    }

    $token = $medecin->createToken('medecinToken')->plainTextToken;
    return response()->json([
        'medecin' => $medecin,
        'token' => $token
    ]);
}



}
