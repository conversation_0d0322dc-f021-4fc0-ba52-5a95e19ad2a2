<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::guard('admin')->check()) {
            return response()->json([
                'error' => 'Accès non autorisé. Connexion admin requise.',
                'code' => 'ADMIN_AUTH_REQUIRED'
            ], 401);
        }

        // Vérifier si c'est bien un admin
        $admin = Auth::guard('admin')->user();
        if (!$admin) {
            return response()->json([
                'error' => 'Accès refusé. Privilèges administrateur requis.',
                'code' => 'ADMIN_PRIVILEGES_REQUIRED'
            ], 403);
        }

        // Optionnel : Vérifier le rôle admin si vous avez un système de rôles
        // if ($admin->role !== 'admin') {
        //     return response()->json([
        //         'error' => 'Accès refusé. Rôle administrateur requis.',
        //         'code' => 'ADMIN_ROLE_REQUIRED'
        //     ], 403);
        // }

        return $next($request);
    }
}
