<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Vérifier si l'utilisateur est connecté (admin pur ou médecin-admin)
        $user = null;

        // Option 1: Admin pur connecté via guard admin
        if (Auth::guard('admin')->check()) {
            $user = Auth::guard('admin')->user();
        }
        // Option 2: Médecin avec privilèges admin connecté via sanctum
        elseif (Auth::guard('sanctum')->check()) {
            $potentialMedecin = Auth::guard('sanctum')->user();

            // Vérifier si c'est un médecin avec privilèges admin
            if ($potentialMedecin instanceof \App\Models\Medecin && $potentialMedecin->isAdmin()) {
                $user = $potentialMedecin;
            }
        }

        // Si aucun utilisateur admin trouvé
        if (!$user) {
            return response()->json([
                'error' => 'Accès non autorisé. Privilèges administrateur requis.',
                'code' => 'ADMIN_AUTH_REQUIRED',
                'message' => 'Vous devez être connecté en tant qu\'administrateur ou médecin-administrateur.'
            ], 401);
        }

        // Ajouter l'utilisateur admin au request pour utilisation dans les contrôleurs
        $request->attributes->set('admin_user', $user);
        $request->attributes->set('admin_type', $user instanceof \App\Models\Medecin ? 'medecin_admin' : 'admin_pur');

        return $next($request);
    }
}
