// import React, { useEffect, useState } from 'react';
// import axios from 'axios';

// const AdminNotifications = () => {
//   const [notifications, setNotifications] = useState([]);

//   useEffect(() => {
//     axios.get('http://localhost:8000/api/admin/notifications')
//       .then(res => setNotifications(res.data));
//   }, []);

//   return (
//     <div className="p-4">
//       <h2 className="text-xl font-bold mb-4">Notifications</h2>
//       {notifications.map((notif, index) => (
//         <div key={index} className="border p-3 mb-2 rounded bg-gray-50">
//           <p>{notif.message}</p>
//           <small>{new Date(notif.created_at).toLocaleString()}</small>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default AdminNotifications;
