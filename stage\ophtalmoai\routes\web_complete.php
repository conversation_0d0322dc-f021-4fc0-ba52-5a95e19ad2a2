<?php
/**
 * Routes Web pour OphthalmoAI
 * Interface utilisateur et pages publiques
 */

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\AuthController;

/*
|--------------------------------------------------------------------------
| ROUTES WEB PUBLIQUES
|--------------------------------------------------------------------------
*/

// Page d'accueil
Route::get('/', function () {
    return view('welcome', [
        'title' => 'OphthalmoAI - Intelligence Artificielle en Ophtalmologie',
        'description' => 'Plateforme d\'assistance médicale en ophtalmologie avec IA'
    ]);
})->name('home');

// Pages d'information
Route::get('/about', function () {
    return view('about', ['title' => 'À propos - OphthalmoAI']);
})->name('about');

Route::get('/services', function () {
    return view('services', ['title' => 'Nos Services - OphthalmoAI']);
})->name('services');

// Interface de questions
Route::prefix('questions')->group(function () {
    Route::get('/', function () {
        return view('questions.index', ['title' => 'Poser une Question']);
    })->name('questions.index');
    
    Route::get('/categories', function () {
        return view('questions.categories', ['title' => 'Catégories de Questions']);
    })->name('questions.categories');
    
    Route::get('/search', function () {
        return view('questions.search', ['title' => 'Rechercher des Questions']);
    })->name('questions.search');
});

// Contact
Route::get('/contact', function () {
    return view('contact', ['title' => 'Nous Contacter']);
})->name('contact');

// Commentaires et témoignages
Route::get('/testimonials', function () {
    return view('testimonials', ['title' => 'Témoignages Patients']);
})->name('testimonials');

/*
|--------------------------------------------------------------------------
| ROUTES D'AUTHENTIFICATION WEB
|--------------------------------------------------------------------------
*/

Route::prefix('auth')->group(function () {
    // Pages de connexion
    Route::get('/login', function () {
        return view('auth.login', ['title' => 'Connexion']);
    })->name('login');
    
    Route::get('/register', function () {
        return view('auth.register', ['title' => 'Inscription']);
    })->name('register');
    
    // Pages spécifiques par type d'utilisateur
    Route::get('/medecin/login', function () {
        return view('auth.medecin-login', ['title' => 'Connexion Médecin']);
    })->name('medecin.login');
    
    Route::get('/medecin/register', function () {
        return view('auth.medecin-register', ['title' => 'Inscription Médecin']);
    })->name('medecin.register');
    
    Route::get('/patient/login', function () {
        return view('auth.patient-login', ['title' => 'Connexion Patient']);
    })->name('patient.login');
    
    Route::get('/patient/register', function () {
        return view('auth.patient-register', ['title' => 'Inscription Patient']);
    })->name('patient.register');
    
    Route::get('/admin/login', function () {
        return view('auth.admin-login', ['title' => 'Connexion Administrateur']);
    })->name('admin.login');
});

/*
|--------------------------------------------------------------------------
| ROUTES PROTÉGÉES WEB
|--------------------------------------------------------------------------
*/

Route::middleware(['auth:sanctum'])->group(function () {
    
    // Dashboard général
    Route::get('/dashboard', function () {
        $user = auth()->user();
        $userType = class_basename($user);
        
        return view('dashboard.index', [
            'title' => 'Tableau de Bord',
            'user' => $user,
            'userType' => $userType
        ]);
    })->name('dashboard');
    
    // Dashboard Médecin
    Route::prefix('medecin')->group(function () {
        Route::get('/dashboard', function () {
            return view('medecin.dashboard', ['title' => 'Dashboard Médecin']);
        })->name('medecin.dashboard');
        
        Route::get('/questions', function () {
            return view('medecin.questions', ['title' => 'Gestion des Questions']);
        })->name('medecin.questions');
        
        Route::get('/patients', function () {
            return view('medecin.patients', ['title' => 'Mes Patients']);
        })->name('medecin.patients');
        
        Route::get('/notifications', function () {
            return view('medecin.notifications', ['title' => 'Notifications']);
        })->name('medecin.notifications');
        
        Route::get('/profile', function () {
            return view('medecin.profile', ['title' => 'Mon Profil']);
        })->name('medecin.profile');
    });
    
    // Dashboard Patient
    Route::prefix('patient')->group(function () {
        Route::get('/dashboard', function () {
            return view('patient.dashboard', ['title' => 'Dashboard Patient']);
        })->name('patient.dashboard');
        
        Route::get('/questions', function () {
            return view('patient.questions', ['title' => 'Mes Questions']);
        })->name('patient.questions');
        
        Route::get('/rendez-vous', function () {
            return view('patient.rendez-vous', ['title' => 'Mes Rendez-vous']);
        })->name('patient.rendez-vous');
        
        Route::get('/profile', function () {
            return view('patient.profile', ['title' => 'Mon Profil']);
        })->name('patient.profile');
    });
    
    // Dashboard Admin
    Route::prefix('admin')->group(function () {
        Route::get('/dashboard', function () {
            return view('admin.dashboard', ['title' => 'Dashboard Administrateur']);
        })->name('admin.dashboard');
        
        Route::get('/users', function () {
            return view('admin.users', ['title' => 'Gestion des Utilisateurs']);
        })->name('admin.users');
        
        Route::get('/questions', function () {
            return view('admin.questions', ['title' => 'Gestion des Questions']);
        })->name('admin.questions');
        
        Route::get('/categories', function () {
            return view('admin.categories', ['title' => 'Gestion des Catégories']);
        })->name('admin.categories');
        
        Route::get('/contacts', function () {
            return view('admin.contacts', ['title' => 'Messages de Contact']);
        })->name('admin.contacts');
        
        Route::get('/stats', function () {
            return view('admin.stats', ['title' => 'Statistiques']);
        })->name('admin.stats');
    });
});

/*
|--------------------------------------------------------------------------
| ROUTES DE DÉVELOPPEMENT
|--------------------------------------------------------------------------
*/

Route::prefix('dev')->group(function () {
    Route::get('/test', function () {
        return view('dev.test', ['title' => 'Page de Test']);
    });
    
    Route::get('/components', function () {
        return view('dev.components', ['title' => 'Test des Composants']);
    });
});

/*
|--------------------------------------------------------------------------
| ROUTES D'API DOCUMENTATION
|--------------------------------------------------------------------------
*/

Route::get('/api/docs', function () {
    return view('api.documentation', ['title' => 'Documentation API']);
})->name('api.docs');

/*
|--------------------------------------------------------------------------
| ROUTES DE FALLBACK
|--------------------------------------------------------------------------
*/

// Route de fallback pour les erreurs 404
Route::fallback(function () {
    return view('errors.404', ['title' => 'Page non trouvée']);
});
