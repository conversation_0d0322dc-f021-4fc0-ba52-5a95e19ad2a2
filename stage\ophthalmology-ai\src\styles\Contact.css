.contact-section {
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  padding: 5rem 0;
  min-height: 100vh;
}

.contact-title {
  color: #006fac;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-align: center;
  position: relative;
}

.contact-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #006fac;
  border-radius: 2px;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-label {
  color: #003d5c;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-control {
  border: 2px solid #e1e8ef;
  border-radius: 8px;
  padding: 0.8rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #006fac;
  box-shadow: 0 0 0 0.2rem rgba(0, 111, 172, 0.25);
}

.contact-btn {
  background: #006fac;
  color: white;
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
}

.contact-btn:hover {
  background: #005c8f;
  transform: translateY(-2px);
}

.contact-info {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.contact-info h4 {
  color: #003d5c;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.contact-info p {
  color: #054e75;
  margin-bottom: 1rem;
}

.contact-info strong {
  color: #006fac;
}

.map-container {
  border-radius: 15px;
  overflow: hidden;
  margin-top: 1.5rem;
}

.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert-error {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

@media (max-width: 768px) {
  .contact-section {
    padding: 3rem 0;
  }

  .contact-title {
    font-size: 2rem;
  }

  .contact-info {
    margin-top: 2rem;
  }
}