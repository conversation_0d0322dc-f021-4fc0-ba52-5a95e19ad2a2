<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MedecinMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Vérifier si l'utilisateur est connecté avec le guard 'sanctum'
        if (!Auth::guard('sanctum')->check()) {
            return response()->json([
                'error' => 'Accès non autorisé. Connexion médecin requise.',
                'code' => 'MEDECIN_AUTH_REQUIRED'
            ], 401);
        }

        // Vérifier si c'est bien un médecin
        $user = Auth::guard('sanctum')->user();
        
        // Vérifier que l'utilisateur est de type Medecin
        if (!$user instanceof \App\Models\Medecin) {
            return response()->json([
                'error' => 'Accès refusé. Compte médecin requis.',
                'code' => 'MEDECIN_ACCOUNT_REQUIRED'
            ], 403);
        }

        // Optionnel : Vérifier que le médecin est actif
        // if (!$user->is_active) {
        //     return response()->json([
        //         'error' => 'Compte médecin désactivé.',
        //         'code' => 'MEDECIN_ACCOUNT_DISABLED'
        //     ], 403);
        // }

        return $next($request);
    }
}
