<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON>vel\Sanctum\HasApiTokens;


class Medecin extends Authenticatable
{
    use HasApiTokens, Notifiable;

    protected $fillable = [
        'nom',
        'email',
        'password',
        'specialite',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];
    public function notifications() {
        return $this->hasMany(Notification::class);
    }
    
}
