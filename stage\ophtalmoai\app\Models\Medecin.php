<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON>vel\Sanctum\HasApiTokens;


class Medecin extends Authenticatable
{
    use HasApiTokens, Notifiable;

    protected $fillable = [
        'nom',
        'email',
        'password',
        'specialite',
        'is_admin',
        'admin_role',
        'admin_granted_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];
    protected $casts = [
        'is_admin' => 'boolean',
        'admin_granted_at' => 'datetime',
    ];

    public function notifications() {
        return $this->hasMany(Notification::class);
    }

    /**
     * Vérifier si le médecin a des privilèges admin
     */
    public function isAdmin(): bool
    {
        return $this->is_admin === true;
    }

    /**
     * Vérifier si le médecin a un rôle admin spécifique
     */
    public function hasAdminRole(string $role): bool
    {
        return $this->is_admin && $this->admin_role === $role;
    }

    /**
     * Vérifier si le médecin est super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasAdminRole('super_admin');
    }

    /**
     * Accorder les privilèges admin
     */
    public function grantAdminPrivileges(string $role = 'admin'): void
    {
        $this->update([
            'is_admin' => true,
            'admin_role' => $role,
            'admin_granted_at' => now(),
        ]);
    }

    /**
     * Révoquer les privilèges admin
     */
    public function revokeAdminPrivileges(): void
    {
        $this->update([
            'is_admin' => false,
            'admin_role' => null,
            'admin_granted_at' => null,
        ]);
    }

}
