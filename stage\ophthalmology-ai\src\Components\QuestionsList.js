// src/components/QuestionsList.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setQuestions, setLoading  , setFilteredQuestions} from '../actions/actions';

const QuestionsList = () => {
  const dispatch = useDispatch();
  const questions = useSelector((state) => state.questions.questions);
  const filteredQuestions = useSelector((state) => state.questions.filteredQuestions);
  const loading = useSelector((state) => state.questions.loading);

  useEffect(() => {
    const fetchQuestions = async () => {
      dispatch(setLoading(true));
      const response = await fetch('/api/questions'); 
      const data = await response.json();
      dispatch(setQuestions(data));
      dispatch(setLoading(false));
    };

    fetchQuestions();
  }, [dispatch]);

  const handleSearch = (searchTerm) => {
    const result = questions.filter((question) =>
      question.text.toLowerCase().includes(searchTerm.toLowerCase())
    );
    dispatch(setFilteredQuestions(result));
  };

  if (loading) {
    return <p>Loading...</p>;
  }
  return (
    <div>
      <input
        type="text"
        placeholder="Search for a question"
        onChange={(e) => handleSearch(e.target.value)}
      />
      <ul>
        {(filteredQuestions.length ? filteredQuestions : questions).map((question) => (
          <li key={question.id}>{question.text}</li>
        ))}
      </ul>
    </div>
  );
};

export default QuestionsList;
