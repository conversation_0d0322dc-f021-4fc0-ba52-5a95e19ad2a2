.footer-section {
  background: linear-gradient(to bottom, #ffffff, #f0f9ff);
  padding-top: 4rem;
  padding-bottom: 2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -2px 10px rgba(0, 139, 213, 0.1);
}

.footer-title {
  color: #006fac;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-text {
  color: #054e75;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-links a {
  color: #054e75;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
  padding: 0.2rem 0;
}

.footer-links a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #006fac;
  transition: width 0.3s ease;
}

.footer-links a:hover {
  color: #006fac;
  transform: translateX(5px);
}

.footer-links a:hover::after {
  width: 100%;
}

.footer-contact li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #054e75;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
  padding: 0.5rem 0;
}

.footer-contact li:hover {
  transform: translateX(5px);
}

.footer-contact svg {
  color: #006fac;
}

.footer-bottom-text {
  color: #054e75;
  font-size: 0.9rem;
  margin: 0;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 111, 172, 0.2);
}

@media (max-width: 768px) {
  .footer-section {
    padding-top: 2rem;
    text-align: center;
  }

  .footer-title {
    justify-content: center;
  }

  .footer-contact li {
    justify-content: center;
  }

  .footer-links {
    align-items: center;
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

.footer-contact li:hover svg {
  animation: float 2s ease-in-out infinite;
}