<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use Illuminate\Http\Request;

class CommentController extends Controller
{

    public function index()
    {
        $comments = Comment::all();  
        return response()->json($comments); 
    }

    // Méthode pour enregistrer un nouveau commentaire
    public function store(Request $request)
{
    $validated = $request->validate([
        'text' => 'required|string',
        'rating' => 'required|integer|min:1|max:5',
        'patient_name' => 'required|string',  // Assurez-vous que le nom du patient est aussi validé
    ]);

    // Création du commentaire
    $comment = Comment::create($validated);  
    
    return response()->json($comment, 201);  // Retourne le commentaire créé
}

}
