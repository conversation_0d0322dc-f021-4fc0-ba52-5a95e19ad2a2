<?php
//api.php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\ReponseController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\MedecinController;
use Illuminate\Http\Request;


Route::prefix('questions')->group(function () {
    Route::get('/getReponse', [QuestionController::class, 'getReponse']);
    Route::get('/', [QuestionController::class, 'index']);
    Route::post('/', [QuestionController::class, 'store']);
    Route::get('/search', [QuestionController::class, 'search']);
});

Route::get('/reponses', [ReponseController::class, 'index']);
Route::post('/reponses', [ReponseController::class, 'store']);
Route::post('/questions/admin', [QuestionController::class, 'storeFromAdmin']);

Route::put('/questions/{id}', [QuestionController::class, 'update']);
Route::delete('/questions/{id}', [QuestionController::class, 'destroy']);
Route::get('/categories', [CategoryController::class, 'index']);


// Route::post('/send-notification', [NotificationController::class, 'sendNotification']);
// Route::get('/notifications/{user_id}', [NotificationController::class, 'getNotifications']);

// Route::get('/medecin/{id}/notifications', [NotificationController::class, 'getNotifications']);

// Récupérer toutes les notifications d’un médecin
// Route::get('/medecins/{medecinId}/notifications', [NotificationController::class, 'getNotifications']);

// Marquer une notification comme lue
// Route::patch('/notifications/{notificationId}/mark-read', [NotificationController::class, 'markNotificationAsRead']);



Route::get('/categories/{id}/questions-frequentes', [CategoryController::class, 'questionsFrequentes']);


// Route pour envoyer un message




// Route pour récupérer les messages d'un médecin
// Route::get('/contact/medecin/{medecinId}', [ContactController::class, 'getMessagesForMedecin']);
// Route::post('/contact/medecin', [ContactController::class, 'sendToDoctor']);

Route::post('/contact', [ContactController::class, 'store']);
Route::get('/medecins/{medecinId}/messages', [ContactController::class, 'getMessagesForMedecin']);


Route::get('/comments', [CommentController::class, 'index']);  // Route pour récupérer les commentaires
Route::post('/comments', [CommentController::class, 'store']);  // Route pour envoyer un nouveau commentaire


// Route::get('/admin/notifications', [AdminController::class, 'getNotifications']);
// Route::post('/admin/login', [AdminController::class, 'login']);


Route::get('/contacts', [ContactController::class, 'index']);
// Route::get('/admin/notifications', [NotificationController::class, 'getAll']);




Route::middleware('auth:sanctum')->group(function () {
   

    // etc...
});
 Route::get('/questions-gestion', [QuestionController::class, 'index']);
    Route::post('/reponses-gestion', [ReponseController::class, 'store']);
    
Route::middleware('auth:sanctum')->get('/medecin-profile', function (Request $request) {
    return $request->user();
});
Route::middleware('auth:sanctum')->get('/notifications', function (Request $request) {
    return $request->user()->notifications()->latest()->get();
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
// Route::post('/login', [AuthController::class, 'login'])->name('login');

Route::get('/questions-gestion', [QuestionController::class, 'getQuestionsGestion']);




    Route::get('/stats-medecin', [MedecinController::class, 'getStats'])
    ->middleware('auth:sanctum');


    Route::get('/medecins/{medecinId}/notifications', [NotificationController::class, 'index']);
Route::patch('/notifications/{id}/mark-read', [NotificationController::class, 'markAsRead']);
Route::patch('/medecins/{medecinId}/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);