<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Question extends Model
{
    use HasFactory;

    protected $fillable = ['texte', 'patient_id', 'necessite_consultation', 'langue', 'category_id'];

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function reponses()
    {
        return $this->hasMany(Reponse::class ,'question_id');
    }
}
