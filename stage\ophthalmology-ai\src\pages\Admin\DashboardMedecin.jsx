import React, { useEffect, useState } from 'react';
import { <PERSON>, Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
} from 'chart.js';
import {
  PeopleFill,
  Bell,
  Calendar3,
  GraphUp,
  CheckCircleFill,
  ClockFill,
  PersonFill,
  ChatDotsFill,
  ExclamationTriangleFill,
  PlusCircleFill,
  ArrowRightCircleFill
} from 'react-bootstrap-icons';
import axios from 'axios';
import './DashboardMedecinNew.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

const DashboardMedecin = () => {
  const [medecin, setMedecin] = useState(null);
  const [stats, setStats] = useState({ labels: [], data: [] });
  const [quickStats, setQuickStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [recentPatients, setRecentPatients] = useState([]);
  const [todayAppointments, setTodayAppointments] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [activityFeed, setActivityFeed] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('medecinToken');
        const medecinNom = localStorage.getItem('medecinNom') || 'Dupont';

        try {
          // Essayer d'abord de récupérer les données depuis l'API
          const [medecinRes, statsRes] = await Promise.all([
            axios.get('http://localhost:8000/api/medecin-profile', {
              headers: { Authorization: `Bearer ${token}` }
            }),
            axios.get('http://localhost:8000/api/stats-medecin', {
              headers: { Authorization: `Bearer ${token}` }
            })
          ]);

          setMedecin(medecinRes.data);
          setStats({
            labels: statsRes.data.labels,
            data: statsRes.data.data
          });

          // Mise à jour des quickStats avec les données réelles
          setQuickStats([
            {
              title: "Consultations Aujourd'hui",
              value: statsRes.data.stats.consultations_today.toString(),
              icon: <PeopleFill />,
              color: "blue"
            },
            {
              title: "Total Consultations",
              value: statsRes.data.stats.total_consultations.toString(),
              icon: <Bell />,
              color: "orange"
            },
            {
              title: "Total Questions",
              value: statsRes.data.stats.total_questions.toString(),
              icon: <Calendar3 />,
              color: "green"
            },
            {
              title: "Taux de Réponse",
              value: `${statsRes.data.stats.response_rate}%`,
              icon: <GraphUp />,
              color: "purple"
            }
          ]);

        } catch (apiError) {
          console.error('Erreur API:', apiError);

          // En développement, utiliser des données simulées
          if (process.env.NODE_ENV !== 'production') {
            console.log('Utilisation de données simulées pour le dashboard');

            // Données simulées pour le médecin
            setMedecin({
              id: 1,
              nom: medecinNom,
              email: `dr.${medecinNom.toLowerCase()}@example.com`,
              specialite: 'Ophtalmologie'
            });

            // Données simulées pour les statistiques
            const today = new Date();
            const labels = Array(7).fill().map((_, i) => {
              const date = new Date(today);
              date.setDate(date.getDate() - (6 - i));
              return date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' });
            });

            const data = [5, 8, 12, 7, 10, 15, 9];

            setStats({
              labels,
              data
            });

            // Données simulées pour les quickStats
            setQuickStats([
              {
                title: "Consultations Aujourd'hui",
                value: "8",
                icon: <PeopleFill />,
                color: "blue"
              },
              {
                title: "Total Consultations",
                value: "124",
                icon: <Bell />,
                color: "orange"
              },
              {
                title: "Total Questions",
                value: "56",
                icon: <ChatDotsFill />,
                color: "green"
              },
              {
                title: "Taux de Réponse",
                value: "92%",
                icon: <GraphUp />,
                color: "purple"
              }
            ]);

            // Données simulées pour les patients récents
            setRecentPatients([
              {
                id: 1,
                nom: 'Martin',
                prenom: 'Sophie',
                age: 45,
                derniere_visite: new Date(today.setDate(today.getDate() - 1)).toISOString(),
                diagnostic: 'Myopie',
                avatar: null
              },
              {
                id: 2,
                nom: 'Dubois',
                prenom: 'Jean',
                age: 62,
                derniere_visite: new Date(today.setDate(today.getDate() - 3)).toISOString(),
                diagnostic: 'Cataracte',
                avatar: null
              },
              {
                id: 3,
                nom: 'Petit',
                prenom: 'Marie',
                age: 35,
                derniere_visite: new Date(today.setDate(today.getDate() - 5)).toISOString(),
                diagnostic: 'Conjonctivite',
                avatar: null
              },
              {
                id: 4,
                nom: 'Leroy',
                prenom: 'Thomas',
                age: 28,
                derniere_visite: new Date().toISOString(),
                diagnostic: 'Astigmatisme',
                avatar: null
              }
            ]);

            // Données simulées pour les rendez-vous d'aujourd'hui
            setTodayAppointments([
              {
                id: 1,
                patient: 'Sophie Martin',
                heure: '09:30',
                motif: 'Suivi myopie',
                statut: 'confirmé'
              },
              {
                id: 2,
                patient: 'Pierre Durand',
                heure: '11:00',
                motif: 'Première consultation',
                statut: 'en attente'
              },
              {
                id: 3,
                patient: 'Isabelle Moreau',
                heure: '14:15',
                motif: 'Contrôle post-opératoire',
                statut: 'confirmé'
              },
              {
                id: 4,
                patient: 'Lucas Bernard',
                heure: '16:30',
                motif: 'Douleurs oculaires',
                statut: 'urgent'
              }
            ]);

            // Données simulées pour les tâches
            setTasks([
              {
                id: 1,
                titre: 'Renouveler ordonnance pour Mme Martin',
                priorite: 'haute',
                complete: false
              },
              {
                id: 2,
                titre: 'Vérifier les résultats d\'analyse de M. Dubois',
                priorite: 'moyenne',
                complete: true
              },
              {
                id: 3,
                titre: 'Appeler le laboratoire pour les lentilles',
                priorite: 'basse',
                complete: false
              },
              {
                id: 4,
                titre: 'Préparer le dossier pour la conférence',
                priorite: 'haute',
                complete: false
              }
            ]);

            // Données simulées pour le flux d'activité
            setActivityFeed([
              {
                id: 1,
                type: 'consultation',
                message: 'Nouvelle consultation avec Sophie Martin',
                date: new Date(today.setHours(today.getHours() - 1)).toISOString()
              },
              {
                id: 2,
                type: 'question',
                message: 'Jean Dubois a posé une question sur son traitement',
                date: new Date(today.setHours(today.getHours() - 3)).toISOString()
              },
              {
                id: 3,
                type: 'rendez-vous',
                message: 'Rendez-vous confirmé avec Marie Petit',
                date: new Date(today.setHours(today.getHours() - 5)).toISOString()
              }
            ]);
          } else {
            // En production, propager l'erreur
            setError('Erreur lors de la récupération des données. Veuillez réessayer plus tard.');
          }
        }
      } catch (err) {
        console.error('Erreur générale:', err);
        setError('Une erreur est survenue. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fonction pour marquer une tâche comme terminée ou non terminée
  const toggleTaskComplete = (taskId) => {
    setTasks(tasks.map(task =>
      task.id === taskId ? { ...task, complete: !task.complete } : task
    ));
  };

  // Afficher un message d'erreur si nécessaire
  if (error) {
    return (
      <div className="error-container">
        <ExclamationTriangleFill size={40} />
        <h3>Erreur</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()} className="retry-button">
          Réessayer
        </button>
      </div>
    );
  }

  // Afficher un spinner de chargement
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Chargement du tableau de bord...</p>
      </div>
    );
  }

  // Données pour le graphique des questions par jour
  const chartData = {
    labels: stats.labels,
    datasets: [{
      label: 'Questions par jour',
      data: stats.data,
      backgroundColor: 'rgba(0, 175, 248, 0.6)',
      borderColor: 'rgba(0, 175, 248, 1)',
      borderWidth: 1
    }]
  };

  // Données pour le graphique de distribution des patients
  const patientDistributionData = {
    labels: ['Nouveaux Patients', 'Patients Réguliers', 'Consultations Urgentes'],
    datasets: [{
      data: [30, 50, 20],
      backgroundColor: [
        'rgba(255, 99, 132, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 206, 86, 0.8)'
      ],
      borderColor: [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)'
      ],
      borderWidth: 1
    }]
  };

  // Données pour le graphique d'évolution des consultations
  const consultationsData = {
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
    datasets: [
      {
        label: 'Consultations 2023',
        data: [65, 59, 80, 81, 56, 55, 40, 45, 60, 70, 75, 80],
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.4,
        fill: true
      },
      {
        label: 'Consultations 2022',
        data: [45, 40, 60, 70, 45, 50, 35, 40, 50, 60, 65, 70],
        borderColor: 'rgba(153, 102, 255, 1)',
        backgroundColor: 'rgba(153, 102, 255, 0.2)',
        tension: 0.4,
        fill: true
      }
    ]
  };

  return (
    <div className="dashboard-content">
      {/* En-tête de bienvenue */}
      <div className="welcome-header fade-in">
        <div>
          <h1>Bienvenue, Dr. {medecin?.nom}</h1>
          <p className="date-display">{new Date().toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}</p>
        </div>
        <div className="welcome-actions">
          <button className="action-button">
            <PlusCircleFill /> Nouveau Patient
          </button>
          <button className="action-button">
            <Calendar3 /> Nouveau Rendez-vous
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="quick-stats-grid fade-in">
        {quickStats.map((stat, index) => (
          <div className={`stat-card ${stat.color}`} key={index}>
            <div className="stat-icon">{stat.icon}</div>
            <div className="stat-info">
              <h3>{stat.title}</h3>
              <p className="stat-value">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Première rangée de widgets */}
      <div className="dashboard-row fade-in">
        {/* Graphique d'activité */}
        <div className="dashboard-card lg">
          <div className="card-header">
            <h3>Activité Médicale</h3>
            <div className="card-actions">
              <select className="period-selector">
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="year">Cette année</option>
              </select>
            </div>
          </div>
          <div className="card-body chart-body">
            <Bar data={chartData} options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'top',
                  labels: {
                    boxWidth: 12,
                    font: { size: 10 }
                  }
                },
                title: { display: false }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  grid: { drawBorder: false },
                  ticks: { font: { size: 10 } }
                },
                x: {
                  grid: { display: false },
                  ticks: { font: { size: 10 } }
                }
              }
            }} />
          </div>
        </div>

        {/* Distribution des patients */}
        <div className="dashboard-card md">
          <div className="card-header">
            <h3>Distribution des Patients</h3>
          </div>
          <div className="card-body chart-body">
            <Doughnut data={patientDistributionData} options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'right',
                  labels: {
                    boxWidth: 10,
                    font: { size: 10 },
                    padding: 5
                  }
                }
              },
              cutout: '70%'
            }} />
          </div>
        </div>
      </div>

      {/* Deuxième rangée de widgets */}
      <div className="dashboard-row fade-in">
        {/* Rendez-vous d'aujourd'hui */}
        <div className="dashboard-card md">
          <div className="card-header">
            <h3>Rendez-vous d'aujourd'hui</h3>
            <div className="card-actions">
              <button className="view-all-button">
                <ArrowRightCircleFill /> Voir tout
              </button>
            </div>
          </div>
          <div className="card-body">
            <div className="appointments-list">
              {todayAppointments.map(appointment => (
                <div
                  key={appointment.id}
                  className={`appointment-item ${appointment.statut === 'urgent' ? 'urgent' : ''}`}
                >
                  <div className="appointment-time">{appointment.heure}</div>
                  <div className="appointment-details">
                    <h4>{appointment.patient}</h4>
                    <p>{appointment.motif}</p>
                  </div>
                  <div className={`appointment-status ${appointment.statut}`}>
                    {appointment.statut === 'confirmé' && <CheckCircleFill />}
                    {appointment.statut === 'en attente' && <ClockFill />}
                    {appointment.statut === 'urgent' && <ExclamationTriangleFill />}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Patients récents */}
        <div className="dashboard-card md">
          <div className="card-header">
            <h3>Patients Récents</h3>
            <div className="card-actions">
              <button className="view-all-button">
                <ArrowRightCircleFill /> Voir tout
              </button>
            </div>
          </div>
          <div className="card-body">
            <div className="patients-list">
              {recentPatients.map(patient => (
                <div key={patient.id} className="patient-item">
                  <div className="patient-avatar">
                    {patient.avatar ? (
                      <img src={patient.avatar} alt={`${patient.prenom} ${patient.nom}`} />
                    ) : (
                      <PersonFill />
                    )}
                  </div>
                  <div className="patient-details">
                    <h4>{patient.prenom} {patient.nom}</h4>
                    <p>{patient.age} ans - {patient.diagnostic}</p>
                  </div>
                  <div className="patient-visit">
                    <small>Dernière visite</small>
                    <p>{new Date(patient.derniere_visite).toLocaleDateString('fr-FR')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Troisième rangée de widgets */}
      <div className="dashboard-row fade-in">
        {/* Tâches à faire */}
        <div className="dashboard-card md">
          <div className="card-header">
            <h3>Tâches à faire</h3>
            <div className="card-actions">
              <button className="add-task-button">
                <PlusCircleFill /> Ajouter
              </button>
            </div>
          </div>
          <div className="card-body">
            <div className="tasks-list">
              {tasks.map(task => (
                <div key={task.id} className={`task-item ${task.complete ? 'completed' : ''}`}>
                  <div className="task-checkbox">
                    <input
                      type="checkbox"
                      checked={task.complete}
                      onChange={() => toggleTaskComplete(task.id)}
                      id={`task-${task.id}`}
                    />
                    <label htmlFor={`task-${task.id}`}></label>
                  </div>
                  <div className="task-content">
                    <p>{task.titre}</p>
                    <div className={`task-priority ${task.priorite}`}>
                      {task.priorite}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Activité récente */}
        <div className="dashboard-card md">
          <div className="card-header">
            <h3>Activité Récente</h3>
          </div>
          <div className="card-body">
            <div className="activity-timeline">
              {activityFeed.map(activity => (
                <div key={activity.id} className="activity-item">
                  <div className={`activity-dot ${activity.type}`}></div>
                  <div className="activity-content">
                    <p>{activity.message}</p>
                    <small>{new Date(activity.date).toLocaleString('fr-FR', {
                      hour: '2-digit',
                      minute: '2-digit',
                      day: 'numeric',
                      month: 'short'
                    })}</small>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Graphique d'évolution des consultations */}
      <div className="dashboard-row fade-in">
        <div className="dashboard-card full">
          <div className="card-header">
            <h3>Évolution des Consultations</h3>
            <div className="card-actions">
              <select className="period-selector">
                <option value="year">Année</option>
                <option value="semester">Semestre</option>
                <option value="quarter">Trimestre</option>
              </select>
            </div>
          </div>
          <div className="card-body chart-body">
            <Line data={consultationsData} options={{
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'top',
                  labels: {
                    boxWidth: 12,
                    font: { size: 10 }
                  }
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  grid: { drawBorder: false },
                  ticks: { font: { size: 10 } }
                },
                x: {
                  grid: { display: false },
                  ticks: { font: { size: 10 } }
                }
              }
            }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardMedecin;
