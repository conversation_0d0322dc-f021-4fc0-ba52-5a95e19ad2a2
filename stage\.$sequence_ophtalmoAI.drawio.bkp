<?xml version="1.0" encoding="UTF-8"?>
<mxGraphModel dx="1056" dy="677" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
  <root>
    <mxCell id="0" />
    <mxCell id="1" parent="0" />
    
    <!-- Acteurs -->
    <mxCell id="2" value="Patient" style="swimlane;" vertex="1" parent="1">
      <mxGeometry x="40" y="100" width="100" height="400" as="geometry" />
    </mxCell>

    <mxCell id="3" value="Système IA" style="swimlane;" vertex="1" parent="1">
      <mxGeometry x="180" y="100" width="100" height="400" as="geometry" />
    </mxCell>

    <mxCell id="4" value="Administrateur" style="swimlane;" vertex="1" parent="1">
      <mxGeometry x="320" y="100" width="100" height="400" as="geometry" />
    </mxCell>

    <!-- Interactions -->
    <mxCell id="5" value="Pose une question" style="edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="2" target="3">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>

    <mxCell id="6" value="Analyse la question" style="edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="3" target="3">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>

    <mxCell id="7" value="Génère une réponse" style="edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="3" target="2">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>

    <mxCell id="8" value="Affiche la réponse" style="edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="2" target="2">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>

    <mxCell id="9" value="Modifie ou ajoute une réponse" style="edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="4" target="3">
      <mxGeometry relative="1" as="geometry" />
    </mxCell>

  </root>
</mxGraphModel>
