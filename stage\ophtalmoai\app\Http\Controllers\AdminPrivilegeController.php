<?php

namespace App\Http\Controllers;

use App\Models\Medecin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminPrivilegeController extends Controller
{
    /**
     * Accorder des privilèges admin à un médecin
     */
    public function grantAdminPrivileges(Request $request, $medecinId)
    {
        $request->validate([
            'admin_role' => 'required|in:admin,super_admin,moderator'
        ]);

        $medecin = Medecin::findOrFail($medecinId);
        
        // Vérifier que l'utilisateur actuel est super admin
        $currentUser = $request->attributes->get('admin_user');
        if (!$currentUser instanceof Medecin || !$currentUser->isSuperAdmin()) {
            if (!$currentUser instanceof \App\Models\Admin) {
                return response()->json([
                    'error' => 'Seuls les super-administrateurs peuvent accorder des privilèges admin.',
                    'code' => 'INSUFFICIENT_PRIVILEGES'
                ], 403);
            }
        }

        $medecin->grantAdminPrivileges($request->admin_role);

        return response()->json([
            'message' => 'Privilèges administrateur accordés avec succès.',
            'medecin' => [
                'id' => $medecin->id,
                'nom' => $medecin->nom,
                'email' => $medecin->email,
                'is_admin' => $medecin->is_admin,
                'admin_role' => $medecin->admin_role,
                'admin_granted_at' => $medecin->admin_granted_at
            ]
        ]);
    }

    /**
     * Révoquer les privilèges admin d'un médecin
     */
    public function revokeAdminPrivileges(Request $request, $medecinId)
    {
        $medecin = Medecin::findOrFail($medecinId);
        
        // Vérifier que l'utilisateur actuel est super admin
        $currentUser = $request->attributes->get('admin_user');
        if (!$currentUser instanceof Medecin || !$currentUser->isSuperAdmin()) {
            if (!$currentUser instanceof \App\Models\Admin) {
                return response()->json([
                    'error' => 'Seuls les super-administrateurs peuvent révoquer des privilèges admin.',
                    'code' => 'INSUFFICIENT_PRIVILEGES'
                ], 403);
            }
        }

        // Empêcher l'auto-révocation
        if ($currentUser->id === $medecin->id) {
            return response()->json([
                'error' => 'Vous ne pouvez pas révoquer vos propres privilèges.',
                'code' => 'SELF_REVOCATION_FORBIDDEN'
            ], 403);
        }

        $medecin->revokeAdminPrivileges();

        return response()->json([
            'message' => 'Privilèges administrateur révoqués avec succès.',
            'medecin' => [
                'id' => $medecin->id,
                'nom' => $medecin->nom,
                'email' => $medecin->email,
                'is_admin' => $medecin->is_admin,
                'admin_role' => $medecin->admin_role
            ]
        ]);
    }

    /**
     * Lister tous les médecins avec leurs privilèges admin
     * TOUS les médecins sont automatiquement admins
     */
    public function listMedecinsWithAdminStatus(Request $request)
    {
        $medecins = Medecin::select([
            'id', 'nom', 'email', 'specialite', 'created_at'
        ])->get();

        return response()->json([
            'message' => 'Tous les médecins ont automatiquement des privilèges administrateur',
            'total_medecins' => $medecins->count(),
            'medecins' => $medecins->map(function ($medecin) {
                return [
                    'id' => $medecin->id,
                    'nom' => $medecin->nom,
                    'email' => $medecin->email,
                    'specialite' => $medecin->specialite,
                    'created_at' => $medecin->created_at,
                    'admin_status' => 'Médecin-Administrateur (automatique)',
                    'is_admin' => true,
                    'privileges' => [
                        'can_manage_users' => true,
                        'can_manage_content' => true,
                        'can_manage_categories' => true,
                        'can_view_statistics' => true
                    ]
                ];
            })
        ]);
    }

    /**
     * Vérifier les privilèges admin de l'utilisateur connecté
     * TOUS les médecins sont automatiquement admins
     */
    public function checkAdminPrivileges(Request $request)
    {
        $user = $request->attributes->get('admin_user');
        $adminType = $request->attributes->get('admin_type');

        $privileges = [
            'is_admin' => true,
            'admin_type' => $adminType,
            'can_manage_users' => true,
            'can_manage_content' => true,
            'can_manage_categories' => true,
            'can_view_statistics' => true,
            'message' => 'Tous les médecins ont des privilèges administrateur'
        ];

        if ($user instanceof Medecin) {
            $privileges['medecin_info'] = [
                'id' => $user->id,
                'nom' => $user->nom,
                'email' => $user->email,
                'specialite' => $user->specialite,
                'admin_status' => 'Médecin-Administrateur (automatique)'
            ];
        } else {
            // Admin pur
            $privileges['admin_info'] = [
                'id' => $user->id,
                'email' => $user->email,
                'admin_status' => 'Administrateur pur'
            ];
        }

        return response()->json($privileges);
    }
}
