<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\Medecin;
use App\Models\Notification;
use Illuminate\Http\Request;
use App\Notifications\NewContactMessage;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    public function store(Request $request)
{
    Log::info('Contact request data:', $request->all());

    try {
        $validated = $request->validate([
            'name' => 'required|string',
            'phone' => 'nullable|string',
            'message' => 'required|string',
            'specialite' => 'nullable|string',
            'medecin_id' => 'nullable|integer',
        ]);

        // Si medecin_id est vide mais une spécialité est fournie, on cherche le médecin
        if (empty($validated['medecin_id']) && !empty($validated['specialite'])) {
            $medecin = Medecin::where('specialite', $validated['specialite'])->first();
            if ($medecin) {
                $validated['medecin_id'] = $medecin->id;
            }
        }

        // Maintenant on peut créer le contact avec un medecin_id (si trouvé)
        $contact = Contact::create($validated);

        // Notification au médecin (si trouvé)
        $medecin = Medecin::find($contact->medecin_id);
        if ($medecin && $medecin->email) {
            $medecin->notify(new NewContactMessage($contact));

            // Enregistrement de la notification
            Notification::create([
                'medecin_id' => $contact->medecin_id,
                'titre' => 'Nouvelle question de ' . $contact->name,
                'contenu' => $contact->message,
                'lu' => false,
            ]);
        }

        return response()->json(['message' => 'Message envoyé avec succès'], 201);

    } catch (\Exception $e) {
        Log::error('Erreur lors de l’envoi du message : ' . $e->getMessage());
        return response()->json(['error' => 'Une erreur est survenue : ' . $e->getMessage()], 500);
    }
}


    public function getMessagesForMedecin($medecinId)
    {
        $messages = Contact::where('medecin_id', $medecinId)->latest()->get();
        return response()->json($messages);
    }
}
