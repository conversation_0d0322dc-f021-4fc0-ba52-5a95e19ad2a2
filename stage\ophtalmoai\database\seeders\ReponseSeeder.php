<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reponse;

class ReponseSeeder extends Seeder {
    public function run() {
        $medecinId = 1;

        $reponses = [
            1 => [
                'texte' => "Les points noirs peuvent être des corps flottants. Consultez si vous voyez aussi des éclairs.",
                'mots_cles' => "points noirs, éclairs",
                'audio_url' => 'audios/points_noirs.mp3'
            ],
            2 => [
                'texte' => "La vision floue comme un voile indique une possible cataracte. Un examen est recommandé.",
                'mots_cles' => "vision floue, voile",
                'audio_url' => 'audios/cataracte_voile.mp3'
            ],
            3 => [
                'texte' => "Le glaucome est souvent indolore mais très dangereux. Faites un contrôle de la pression oculaire.",
                'mots_cles' => "glaucome, douleur, pression",
                'audio_url' => 'audios/glaucome.mp3'
            ],
            4 => [
                'texte' => "Rester devant un écran fatigue les yeux mais ce n’est pas dangereux. Faites des pauses.",
                'mots_cles' => "écran, fatigue",
                'audio_url' => 'audios/ecran.mp3'
            ]
        ];

        foreach ($reponses as $i => $rep) {
            // une réponse par langue (chaque 3 questions sont les mêmes en fr/en/darija)
            foreach ([0,1,2] as $offset) {
                Reponse::create([
                    'texte' => $rep['texte'],
                    'mots_cles' => $rep['mots_cles'],
                    'audio_url' => $rep['audio_url'],
                    'question_id' => ($i - 1) * 3 + $offset + 1,
                    'medecin_id' => $medecinId
                ]);
            }
        }
    }
}
