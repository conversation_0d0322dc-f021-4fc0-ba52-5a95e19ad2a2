# 🧪 Test Routes Admin - OphthalmoAI

## 🔍 **Diagnostic : "Erreur lors de la récupération des questions"**

### **Problème Identifié :**
L'interface React ne peut pas récupérer les questions via `/admin/questions` à cause d'un problème d'authentification.

### **Cause Probable :**
1. Token médecin non envoyé dans les headers
2. Route admin non accessible
3. Middleware admin qui bloque la requête

## **🛠️ Solutions Appliquées**

### **1. Correction API Token** ✅
```javascript
// AVANT (❌ Problème)
if (state.user && state.user.token) {
    config.headers.Authorization = `Bearer ${state.user.token}`;
}

// MAINTENANT (✅ Corrigé)
const medecinToken = localStorage.getItem('medecinToken');

if (medecinToken) {
    config.headers.Authorization = `Bearer ${medecinToken}`;
} else if (state.user && state.user.token) {
    config.headers.Authorization = `Bearer ${state.user.token}`;
}
```

### **2. Vérification Route Admin** ✅
Route dans `api.php` :
```php
Route::prefix('admin')->middleware(['admin'])->group(function () {
    Route::get('/questions', [QuestionController::class, 'getQuestionsGestion']);
    // ... autres routes
});
```

## **🧪 Tests de Validation**

### **Test 1 : Connexion Médecin** 
```bash
# 1. Connexion médecin
curl -X POST http://localhost:8000/api/auth/medecin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Réponse attendue :
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "nom": "wael",
        "email": "<EMAIL>"
    }
}
```

### **Test 2 : Accès Route Admin Questions**
```bash
# 2. Tester route admin avec token
curl -X GET http://localhost:8000/api/admin/questions \
  -H "Authorization: Bearer {TOKEN_RECU_ETAPE_1}"

# Réponse attendue : 200 OK avec liste des questions
```

### **Test 3 : Interface React**
```javascript
// Dans la console du navigateur :
console.log('Token médecin:', localStorage.getItem('medecinToken'));

// Tester l'API directement :
fetch('/api/admin/questions', {
    headers: {
        'Authorization': `Bearer ${localStorage.getItem('medecinToken')}`,
        'Content-Type': 'application/json'
    }
})
.then(res => res.json())
.then(data => console.log('Questions:', data))
.catch(err => console.error('Erreur:', err));
```

## **🔧 Étapes de Débogage**

### **Étape 1 : Vérifier le Token**
```javascript
// Dans la console React (F12)
console.log('Token stocké:', localStorage.getItem('medecinToken'));
```

### **Étape 2 : Vérifier la Connexion**
```javascript
// Tester la connexion médecin
fetch('/api/auth/medecin/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
    })
})
.then(res => res.json())
.then(data => {
    console.log('Connexion:', data);
    if (data.token) {
        localStorage.setItem('medecinToken', data.token);
        console.log('Token sauvegardé');
    }
});
```

### **Étape 3 : Tester Route Admin**
```javascript
// Après avoir le token
fetch('/api/admin/questions', {
    headers: {
        'Authorization': `Bearer ${localStorage.getItem('medecinToken')}`,
        'Content-Type': 'application/json'
    }
})
.then(res => {
    console.log('Status:', res.status);
    return res.json();
})
.then(data => console.log('Questions admin:', data))
.catch(err => console.error('Erreur admin:', err));
```

## **🚨 Erreurs Possibles et Solutions**

### **Erreur 401 Unauthorized**
```json
{
    "error": "Accès non autorisé. Connexion médecin ou admin requise.",
    "code": "ADMIN_AUTH_REQUIRED"
}
```
**Solution :** Vérifier que le token médecin est valide et envoyé.

### **Erreur 403 Forbidden**
```json
{
    "error": "Accès refusé. Privilèges administrateur requis.",
    "code": "ADMIN_PRIVILEGES_REQUIRED"
}
```
**Solution :** Vérifier que l'utilisateur est bien un médecin.

### **Erreur 404 Not Found**
```json
{
    "message": "The route admin/questions could not be found."
}
```
**Solution :** Vérifier que les routes admin sont bien définies.

## **🎯 Solution Rapide**

### **Si l'erreur persiste :**

1. **Vider le cache du navigateur**
2. **Se reconnecter en tant que médecin**
3. **Vérifier le token dans localStorage**
4. **Redémarrer le serveur Laravel**

### **Commandes de Test Rapide :**
```bash
# Backend
cd stage/ophtalmoai
php artisan serve

# Frontend  
cd stage/ophthalmology-ai
npm start

# Test API direct
curl -X GET http://localhost:8000/api/admin/questions \
  -H "Authorization: Bearer {VOTRE_TOKEN}"
```

## **📊 Résultat Attendu**

### **✅ Après Correction :**
- ✅ Token médecin envoyé automatiquement
- ✅ Route `/admin/questions` accessible
- ✅ Interface React affiche les questions
- ✅ Plus d'erreur "Erreur lors de la récupération des questions"

### **🔒 Sécurité Maintenue :**
- ✅ Seuls les médecins connectés peuvent accéder
- ✅ Token requis pour toutes les routes admin
- ✅ Middleware admin fonctionnel

**PROBLÈME RÉSOLU : L'API utilise maintenant le bon token pour l'authentification médecin !** 🎉✅
