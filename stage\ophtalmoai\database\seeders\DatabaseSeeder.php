<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use PatientSeeder;
use MedecinSeeder;
use QuestionSeeder;
use ReponseSeeder;
use Illuminate\Database\Seeder;


use DB;
class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void {
        $this->call([
        
            QuestionSeeder::class,
            ReponseSeeder::class,
        ]);
    }
    
}
