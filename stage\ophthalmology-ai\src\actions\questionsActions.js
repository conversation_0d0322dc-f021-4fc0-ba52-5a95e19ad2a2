

export const fetchQuestions = () => {
    return async (dispatch) => {
      dispatch({ type: 'SET_LOADING', payload: true }); // Démarre le chargement
      try {
        const response = await fetch('http://127.0.0.1:8000/api/questions');
        const data = await response.json();

        dispatch({
          type: 'SET_QUESTIONS',
          payload: data, // Envoie toutes les questions au store
        });
      } catch (error) {
        console.error('Erreur lors de la récupération des questions:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };
  };




export const fetchResponse = (question) => async (dispatch) => {
    try {
      // Indiquer que la recherche est en cours
      dispatch({ type: 'SET_LOADING', payload: true });

      // Encoder la question pour éviter les problèmes avec les caractères spéciaux
      const encodedQuestion = encodeURIComponent(question);

      const response = await fetch(`http://127.0.0.1:8000/api/questions/getReponse?question=${encodedQuestion}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Erreur inconnue' }));
        throw new Error(errorData.message || `Erreur ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Si aucune donnée n'est retournée ou si la réponse est vide
      if (!data || (Array.isArray(data) && data.length === 0)) {
        dispatch({
          type: 'SET_ERROR',
          payload: 'Aucune réponse trouvée pour cette question. Essayez de reformuler votre question.'
        });
      } else {
        dispatch({
          type: 'SET_FILTERED_QUESTIONS',
          payload: data, // Envoie les questions filtrées au store
        });
      }
    } catch (error) {
      console.error('Erreur lors de la recherche :', error);
      dispatch({
        type: 'SET_ERROR',
        payload: error.message || 'Une erreur est survenue lors de la recherche. Veuillez réessayer.'
      });
    } finally {
      // Indiquer que la recherche est terminée
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };
