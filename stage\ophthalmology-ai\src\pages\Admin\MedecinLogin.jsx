import React, { useState } from 'react';
import axios from 'axios';
import { UserCircle2, Lock, LogIn } from 'lucide-react';
import './MedecinLogin.css';

const MedecinLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [erreur, setErreur] = useState('');

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      const res = await axios.post('http://localhost:8000/api/login', {
        email,
        password
      });

      localStorage.setItem('medecinToken', res.data.token);
      localStorage.setItem('medecinNom', res.data.medecin.nom);
      localStorage.setItem('medecinId', res.data.medecin.id);

      // Rediriger vers le nouveau dashboard
      window.location.href = "/medecin/dashboard";
    } catch (err) {
      setErreur("Email ou mot de passe incorrect.");
    }
  };

  return (
    <div className="login-container" style={styles.container}>
      <h2 style={styles.heading}>Connexion Médecin</h2>
      {erreur && <p style={styles.error}>{erreur}</p>}
      <form onSubmit={handleLogin} style={styles.form}>
        <input
          type="email"
          placeholder="Adresse e-mail"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          style={styles.input}
        /><br />
        <input
          type="password"
          placeholder="Mot de passe"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          style={styles.input}
        /><br />
        <button type="submit" style={styles.button}>Se connecter</button>
        <p style={styles.registerText}>Vous n'avez pas de compte ? <a href="/register-medecin" style={styles.link}>Créer un compte</a></p>
      </form>
    </div>
  );
};

const styles = {
  container: {
    backgroundColor: "#eff9ff", // background color similar to blue-whale-100
    padding: '30px',
    borderRadius: '8px',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '400px',
    margin: 'auto',
    textAlign: 'center',
  },
  heading: {
    fontSize: '24px',
    color: '#006fac', // blue-whale-500 for the heading
    marginBottom: '20px',
  },
  error: {
    color: '#ff4d4d', // Red color for errors
    marginBottom: '10px',
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
  },
  input: {
    padding: '10px',
    marginBottom: '15px',
    borderRadius: '5px',
    border: '1px solid #b5e8ff', // blue-whale-200 for input borders
    fontSize: '16px',
  },
  button: {
    backgroundColor: '#008bd5', // blue-whale-700 for button color
    color: 'white',
    padding: '12px',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '16px',
  },
  registerText: {
    marginTop: '15px',
    color: '#008bd5', // blue-whale-700 for the text
  },
  link: {
    color: '#008bd5',
    textDecoration: 'none',
  }
};

export default MedecinLogin;
