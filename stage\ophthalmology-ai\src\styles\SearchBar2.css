.search-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.search-hero {
  padding: 4rem 2rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.search-title {
  font-size: 2.5rem;
  color: #006fac;
  margin-bottom: 2rem;
  font-weight: 700;
}

.title-accent {
  color: #ff6b6b;
  margin-left: 0.5rem;
}

.search-input-group {
  position: relative;
  max-width: 700px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 1.2rem 3.5rem 1.2rem 1.5rem;
  border: 2px solid #e1e8ef;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #006fac;
  box-shadow: 0 0 0 4px rgba(0, 111, 172, 0.1);
}

.clear-button {
  position: absolute;
  right: 120px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: #f0f0f0;
  color: #333;
}

.search-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: #006fac;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-button:hover {
  background: #005c8f;
}

.search-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.typing-indicator {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.recent-searches {
  margin-top: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recent-searches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.recent-searches h5 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  color: #003d5c;
}

.clear-recent {
  background: none;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.clear-recent:hover {
  color: #ff6b6b;
}

.recent-searches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.recent-search-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #444;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-search-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  animation: float 6s ease-in-out infinite;
  opacity: 0.2;
}

.icon-1 { top: 20%; left: 15%; animation-delay: 0s; }
.icon-2 { top: 40%; right: 20%; animation-delay: 2s; }
.icon-3 { bottom: 30%; left: 30%; animation-delay: 4s; }

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-title {
    font-size: 2rem;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-button {
    position: relative;
    right: auto;
    transform: none;
    margin-top: 1rem;
    width: 100%;
    justify-content: center;
  }

  .clear-button {
    right: 10px;
  }
}