import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchQuestions, fetchResponse } from '../actions/questionsActions';
import DoctorForm from './DoctorForm';
import { Search, MessageCircleQuestion, Stethoscope, SmilePlus } from 'lucide-react';

const SearchBar2 = () => {
  const dispatch = useDispatch();
  const [question, setQuestion] = useState('');
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [showDoctorForm, setShowDoctorForm] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const response = useSelector((state) => state.questions.filteredQuestions);
  const questionsList = useSelector((state) => state.questions.questions);

  const popularSuggestions = [
    'Je vois flou',
    'J’ai mal aux yeux',
    'Ma vision baisse',
    'J’ai un point noir',
    'Yeux secs',
  ];

  const randomQuestions = [
    "Pourquoi j’ai des taches dans la vision ?",
    "Est-ce normal de voir flou le soir ?",
    "Un enfant peut-il avoir un glaucome ?",
    "Quand consulter pour une cataracte ?"
  ];
  const [suggestionOfDay, setSuggestionOfDay] = useState('');

  useEffect(() => {
    dispatch(fetchQuestions());
    const random = randomQuestions[Math.floor(Math.random() * randomQuestions.length)];
    setSuggestionOfDay(random);
  }, [dispatch]);

  useEffect(() => {
    if (question.length > 1) {
      const suggestions = questionsList.filter((q) =>
        q.texte.toLowerCase().includes(question.toLowerCase())
      );
      setFilteredSuggestions(suggestions);
    } else {
      setFilteredSuggestions([]);
    }
  }, [question, questionsList]);

  const handleSearch = (e) => {
    e.preventDefault();
    dispatch(fetchResponse(question));
  };

  const handleSuggestionClick = (suggestion) => {
    setQuestion(suggestion);
    dispatch(fetchResponse(suggestion));
    setFilteredSuggestions([]);
  };

  return (
    <div className="container py-5">
      <div className="row justify-content-center">
        <div className="col-lg-8">
          <h3 className="mb-4 fw-bold d-flex align-items-center text-blue-whale">
            {/* <MessageCircleQuestion size={28} className="me-2" /> */}
            Posez votre question ophtalmologique
          </h3>

          <form onSubmit={handleSearch}>
            <div className="input-group input-group-lg mb-4 shadow-sm rounded-4 border border-light overflow-hidden">
              <input
                type="text"
                className="form-control fs-5 border-0 focus-ring"
                placeholder="Ex: Douleur à l’œil, vision floue..."
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                style={{ transition: 'all 0.3s ease-in-out' }}
              />
              <button
                type="submit"
                className="btn btn-blue-whale px-4"
                disabled={!question}
              >
                <Search className="me-1" /> Rechercher
              </button>
            </div>
          </form>

          {filteredSuggestions.length > 0 && (
            <div className="list-group mb-4">
              {filteredSuggestions.map((q, index) => (
                <button
                  key={index}
                  type="button"
                  className="list-group-item list-group-item-action fs-6 py-3 text-blue-whale"
                  onClick={() => handleSuggestionClick(q.texte)}
                  style={{
                    transition: 'background 0.2s',
                    borderRadius: '8px',
                  }}
                >
                  {q.texte}
                </button>
              ))}
            </div>
          )}

          <div className="mb-5">
            <p className="text-muted mb-2 fw-medium text-blue-whale"> Suggestions populaires :</p>
            <div className="d-flex flex-wrap gap-3">
              {popularSuggestions.map((sugg, idx) => (
                <span
                  key={idx}
                  className="badge bg-white border border-blue-whale text-blue-whale fs-6 px-3 py-2 rounded-pill"
                  style={{ cursor: 'pointer', transition: '0.3s' }}
                  onClick={() => handleSuggestionClick(sugg)}
                  onMouseOver={(e) => e.target.classList.add('bg-blue-whale', 'text-white')}
                  onMouseOut={(e) => e.target.classList.remove('bg-blue-whale', 'text-white')}
                >
                  {sugg}
                </span>
              ))}
            </div>
          </div>

          {suggestionOfDay && (
            <div className="alert alert-light border-start border-4 border-blue-whale mb-4 fs-6">
              <strong>Suggestion du jour :</strong> {suggestionOfDay}
            </div>
          )}

         {/* MODAL POUR AFFICHER LA RÉPONSE */}
{response && (
  <div className="modal fade show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content rounded-4">
        <div className="modal-header">
          <h5 className="modal-title">Résultat de votre recherche</h5>
          <button type="button" className="btn-close" onClick={() => dispatch({ type: 'CLEAR_RESPONSE' })}></button>
        </div>
        <div className="modal-body">
          {response.necessite_consultation ? (
            <>
              <h5 className="fw-semibold d-flex align-items-center text-blue-whale mb-3">
                <Stethoscope className="me-2" />
                Conseil médical
              </h5>
              <p>{response.message}</p>
              <p>Souhaitez-vous contacter un médecin ?</p>
              <div className="d-flex gap-3 mb-3">
                <button className="btn btn-success rounded-pill px-4" onClick={() => setShowDoctorForm(true)}>
                  Oui
                </button>
                <button className="btn btn-outline-secondary rounded-pill px-4" onClick={() => setShowDoctorForm(false)}>
                  Non
                </button>
              </div>
              {showDoctorForm && (
                <div className="mt-3">
                  <DoctorForm specialite={response.specialite || 'Ophtalmologue'} />
                </div>
              )}
            </>
          ) : (
            <div className="d-flex align-items-center">
              <span>{response.message}</span>
            </div>
          )}
        </div>
        <div className="modal-footer">
          <button className="btn btn-outline-secondary" onClick={() => dispatch({ type: 'CLEAR_RESPONSE' })}>
            Fermer
          </button>
        </div>
      </div>
    </div>
  </div>
)}

        </div>
      </div>
    </div>
  );
};

export default SearchBar2;