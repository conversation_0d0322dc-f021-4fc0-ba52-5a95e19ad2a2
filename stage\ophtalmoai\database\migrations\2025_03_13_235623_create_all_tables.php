<?php 

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        // Table des patients
        Schema::create('patients', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('email')->unique();
            $table->timestamps();
        });

        // Table des médecins
        Schema::create('medecins', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('email')->unique();
            $table->string('password'); 
            $table->string('specialite');
            $table->timestamps();
        });
        

        Schema::create('rendez_vous', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->foreignId('medecin_id')->constrained('medecins')->onDelete('cascade');
            $table->dateTime('date_heure')->nullable();
            $table->text('motif')->nullable();
            $table->enum('statut', ['en attente', 'confirmé', 'annulé'])->default('en attente');
            $table->timestamps();
        });
        
 
        // Table des catégories
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->timestamps();
        });

        // Table des questions
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->text('texte');
            $table->foreignId('patient_id')->constrained('patients')->onDelete('cascade');
            $table->boolean('necessite_consultation')->default(false);
            $table->enum('langue', ['fr', 'en', 'darija'])->default('fr');
            $table->fullText('texte');
            $table->foreignId('category_id')->nullable()->constrained('categories')->nullOnDelete();
            $table->timestamps();
        });

        // Table des réponses
        Schema::create('reponses', function (Blueprint $table) {
            $table->id();
            $table->text('texte')->nullable();
            $table->string('audio_url')->nullable();
            $table->string('mots_cles')->nullable();
            $table->foreignId('medecin_id')->nullable()->constrained('medecins')->cascadeOnDelete();
            $table->foreignId('question_id')->constrained('questions')->cascadeOnDelete();
            $table->timestamps();
        });

        // Table des contacts
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            // $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('message');
            $table->timestamps();
        });

        // Table des tokens personnels
        Schema::create('personal_access_tokens', function (Blueprint $table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }
  
   
 
        


    public function down() {
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('contacts');
        Schema::dropIfExists('reponses');
        Schema::dropIfExists('questions');
        Schema::dropIfExists('categories');
        Schema::dropIfExists('medecins');
        Schema::dropIfExists('patients');
        Schema::dropIfExists('rendez_vous');

    }
};
