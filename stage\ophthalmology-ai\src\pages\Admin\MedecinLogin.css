.login-container {
  animation: fadeIn 0.3s ease-out;
}

.login-container input:focus {
  box-shadow: 0 0 0 2px rgba(0, 111, 172, 0.2);
}

.login-container button {
  position: relative;
  overflow: hidden;
}

.login-container button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.login-container button:hover::before {
  left: 100%;
}

.login-container a {
  position: relative;
}

.login-container a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #008bd5;
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.login-container a:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation pour l'erreur */
.bg-red-50 {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Amélioration des effets de hover */
.hover\:opacity-90:hover {
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  transform: translateY(-1px);
}

/* Style de focus amélioré pour les inputs */
input:focus {
  transition: all 0.2s ease-in-out;
}

/* Animation du gradient de fond */
.bg-gradient-to-b {
  background-size: 200% 200%;
  animation: gradientMove 15s ease infinite;
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}