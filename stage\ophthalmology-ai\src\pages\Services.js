import React from "react";

const Services = () => {
  const servicesList = [
    {
      title: "Diagnostic Assisté par IA",
      description: "Analyse automatique des images ophtalmiques pour un diagnostic préliminaire rapide et précis.",
      icon: "🔍",
    },
    {
      title: "Suivi des Pathologies",
      description: "Suivi de l'évolution des maladies oculaires comme le glaucome, la cataracte et la rétinopathie.",
      icon: "📊",
    },
    {
      title: "Consultation Virtuelle",
      description: "Posez vos questions et obtenez des réponses instantanées grâce à l'IA spécialisée en ophtalmologie.",
      icon: "💬",
    },
  ];

  return (
    <div className="container my-5">
      <h2 className="text-center mb-4">Nos Services</h2>
      <div className="row">
        {servicesList.map((service, index) => (
          <div key={index} className="col-md-4">
            <div className="service-card p-4 text-center">
              <div className="service-icon">{service.icon}</div>
              <h4 className="mt-3">{service.title}</h4>
              <p>{service.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Services;
