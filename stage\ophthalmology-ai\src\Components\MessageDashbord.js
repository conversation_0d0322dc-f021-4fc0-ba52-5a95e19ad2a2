import React, { useEffect, useState } from 'react';
import axios from 'axios';

const MessagesDashboard = ({ medecinId }) => {
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    axios.get(`/api/medecins/${medecinId}/messages`)
      .then(res => setMessages(res.data))
      .catch(err => console.error(err));
  }, [medecinId]);

  return (
    <div>
      <h2>Messages reçus</h2>
      {messages.map((msg, index) => (
        <div key={index} className="message-card">
          <h4>{msg.name} ({msg.email})</h4>
          <p>{msg.message}</p>
        </div>
      ))}
    </div>
  );
};

export default MessagesDashboard;
