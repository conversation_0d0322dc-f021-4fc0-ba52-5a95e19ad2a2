import React, { useState, useEffect } from "react";
import api from "../../api";
import { <PERSON> } from "react-router-dom";
import { PencilSquare, Trash, Eye } from 'react-bootstrap-icons';

const QuestionsManagement = () => {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState(null);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [questionsPerPage] = useState(10);

  useEffect(() => {
    fetchQuestions();
  }, []);

  const fetchQuestions = async () => {
    try {
      setLoading(true);

      // Vérifier le token avant la requête
      const token = localStorage.getItem('medecinToken');
      console.log('Token médecin:', token ? 'Présent' : 'Absent');

      const response = await api.get('/admin/questions');
      console.log('Questions reçues:', response.data); // Pour déboguer
      setQuestions(response.data);
      setError(null);
    } catch (error) {
      console.error('Erreur complète:', error);
      console.error('Status:', error.response?.status);
      console.error('Data:', error.response?.data);

      let errorMessage = 'Erreur lors de la récupération des questions';

      if (error.response?.status === 401) {
        errorMessage = 'Non autorisé. Veuillez vous reconnecter.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Accès refusé. Privilèges administrateur requis.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Route non trouvée. Vérifiez la configuration.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {
      try {
        await api.delete(`/admin/questions/${id}`);
        setQuestions(questions.filter((q) => q.id !== id));
        alert("Question supprimée avec succès");
      } catch (error) {
        alert("Erreur lors de la suppression de la question");
        console.error("Erreur lors de la suppression de la question", error);
      }
    }
  };

  const filteredQuestions = questions.filter(question =>
    question.texte.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (question.categorie?.nom || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);
  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Generate page numbers
  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  if (loading) {
    return (
      <div className="container mt-4 text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mt-4">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h2>Gestion des Questions</h2>
        <Link to="/questions/ajouter" className="btn btn-primary">
          + Ajouter une question
        </Link>
      </div>

      <div className="card shadow-sm mb-4">
        <div className="card-body">
          <div className="mb-3">
            <input
              type="text"
              className="form-control"
              placeholder="Rechercher une question ou une catégorie..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page on search
              }}
            />
          </div>

          <div className="table-responsive">
            <table className="table table-hover table-bordered align-middle">
              <thead className="table-primary text-center">
                <tr>
                  <th>ID</th>
                  <th>Question</th>
                  <th>Réponse</th>
                  <th>Catégorie</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentQuestions.length > 0 ? (
                  currentQuestions.map((question) => (
                    <tr key={question.id}>
                      <td className="text-center">{question.id}</td>
                      <td>{question.texte}</td>
                      <td>{question.reponse?.texte || <span className="text-muted">Non définie</span>}</td>
                      <td>{question.categorie?.nom || <span className="text-muted">Aucune</span>}</td>
                      <td className="text-center">
                        <Link
                          to={`/questions/details/${question.id}`}
                          className="btn btn-sm btn-outline-info mx-1"
                          title="Voir les détails"
                        >
                          <Eye />
                        </Link>
                        <Link
                          to={`/questions/modifier/${question.id}`}
                          className="btn btn-sm btn-outline-warning mx-1"
                          title="Modifier"
                        >
                          <PencilSquare />
                        </Link>
                        <button
                          className="btn btn-sm btn-outline-danger mx-1"
                          onClick={() => handleDelete(question.id)}
                          title="Supprimer"
                        >
                          <Trash />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="5" className="text-center text-muted">
                      {searchTerm ? "Aucun résultat trouvé" : "Aucune question disponible"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredQuestions.length > questionsPerPage && (
            <nav aria-label="Questions pagination" className="mt-4">
              <ul className="pagination justify-content-center">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Précédent
                  </button>
                </li>
                
                {pageNumbers.map(number => (
                  <li key={number} className={`page-item ${currentPage === number ? 'active' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => paginate(number)}
                    >
                      {number}
                    </button>
                  </li>
                ))}
                
                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Suivant
                  </button>
                </li>
              </ul>
              <div className="text-center mt-2">
                <small className="text-muted">
                  Affichage de {indexOfFirstQuestion + 1} à {Math.min(indexOfLastQuestion, filteredQuestions.length)} sur {filteredQuestions.length} questions
                </small>
              </div>
            </nav>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionsManagement;
