<?php

namespace App\Http\Controllers;

use App\Models\Question;
use App\Models\Contact;
use Illuminate\Http\Request;

class MedecinController
{
    public function getStats()
{
    // Récupérer l'ID du médecin connecté
    $medecinId = auth()->id();

    // Obtenir la date d'il y a 7 jours
    $startDate = now()->subDays(7);

    // Récupérer les questions par jour pour les 7 derniers jours
    $questionsParJour = Question::where('medecin_id', $medecinId)
        ->where('created_at', '>=', $startDate)
        ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

    // Récupérer les demandes de consultation
    $consultationsAujourdhui = Contact::where('medecin_id', $medecinId)
        ->whereDate('created_at', today())
        ->count();

    // Préparer les données pour le graphique
    $labels = [];
    $data = [];

    // Remplir avec des données pour chaque jour
    for ($i = 6; $i >= 0; $i--) {
        $date = now()->subDays($i)->format('Y-m-d');
        $labels[] = now()->subDays($i)->format('d/m');
        
        $count = $questionsParJour
            ->where('date', $date)
            ->first()
            ?->count ?? 0;
            
        $data[] = $count;
    }

    // Calculer le taux de réponse
    $totalQuestions = Question::where('medecin_id', $medecinId)->count();
    $questionsRepondues = Question::where('medecin_id', $medecinId)
        ->whereNotNull('reponse')
        ->count();
    
    $tauxReponse = $totalQuestions > 0 
        ? round(($questionsRepondues / $totalQuestions) * 100) 
        : 100;

    // Questions en attente
    $questionsEnAttente = Question::where('medecin_id', $medecinId)
        ->whereNull('reponse')
        ->count();

    return response()->json([
        'labels' => $labels,
        'data' => $data,
        'stats' => [
            'consultations_today' => $consultationsAujourdhui,
            'pending_questions' => $questionsEnAttente,
            'total_questions' => $totalQuestions,
            'response_rate' => $tauxReponse
        ]
    ]);
}




    
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
