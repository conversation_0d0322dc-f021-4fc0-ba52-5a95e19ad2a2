import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const MedecinRegister = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    password: '',
    confirmPassword: '',
    specialite: '',
  });
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (formData.password !== formData.confirmPassword) {
      setMessage("Les mots de passe ne correspondent pas.");
      setIsSubmitting(false);
      return;
    }

    if (formData.password.length < 6) {
      setMessage("Le mot de passe doit contenir au moins 6 caractères.");
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await axios.post('http://localhost:8000/api/register', formData);
      setMessage("✅ Inscription réussie !");
      setTimeout(() => navigate('/login-medecin'), 2000);
    } catch (error) {
      setMessage(error.response?.data?.message || "Une erreur est survenue");
    } finally {
      setIsSubmitting(false);
    }
  };

  const styles = {
    container: {
      backgroundColor: "#eff9ff",
      padding: '30px',
      borderRadius: '8px',
      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
      width: '100%',
      maxWidth: '500px',
      margin: 'auto',
      marginTop: '2rem',
    },
    heading: {
      fontSize: '24px',
      color: '#006fac',
      marginBottom: '20px',
      textAlign: 'center',
    },
    form: {
      display: 'flex',
      flexDirection: 'column',
      gap: '15px',
    },
    inputGroup: {
      display: 'flex',
      flexDirection: 'column',
      gap: '5px',
    },
    label: {
      color: '#006fac',
      fontSize: '0.95rem',
      fontWeight: '500',
    },
    input: {
      padding: '12px',
      borderRadius: '5px',
      border: '1px solid #b5e8ff',
      fontSize: '16px',
      transition: 'all 0.3s ease',
      outline: 'none',
      '&:focus': {
        borderColor: '#008bd5',
        boxShadow: '0 0 0 2px rgba(0, 139, 213, 0.2)',
      },
    },
    button: {
      backgroundColor: '#008bd5',
      color: 'white',
      padding: '12px',
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
      fontSize: '16px',
      fontWeight: '500',
      marginTop: '10px',
      transition: 'background-color 0.3s ease',
      '&:hover': {
        backgroundColor: '#006fac',
      },
      '&:disabled': {
        backgroundColor: '#b5e8ff',
        cursor: 'not-allowed',
      },
    },
    message: {
      textAlign: 'center',
      padding: '10px',
      marginBottom: '15px',
      borderRadius: '5px',
      backgroundColor: (msg) => msg.startsWith('✅') ? '#dcfce7' : '#fee2e2',
      color: (msg) => msg.startsWith('✅') ? '#166534' : '#991b1b',
    },
    loginLink: {
      textAlign: 'center',
      marginTop: '15px',
      color: '#006fac',
    },
    link: {
      color: '#008bd5',
      textDecoration: 'none',
      fontWeight: '500',
      '&:hover': {
        textDecoration: 'underline',
      },
    },
  };

  return (
    <div style={styles.container}>
      <h2 style={styles.heading}>Inscription Médecin</h2>
      
      {message && (
        <div style={styles.message}>
          {message}
        </div>
      )}

      <form onSubmit={handleRegister} style={styles.form}>
        <div style={styles.inputGroup}>
          <label style={styles.label}>Nom complet *</label>
          <input
            type="text"
            name="nom"
            value={formData.nom}
            onChange={handleChange}
            style={styles.input}
            required
            placeholder="Dr. John Doe"
          />
        </div>

        <div style={styles.inputGroup}>
          <label style={styles.label}>Email *</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            style={styles.input}
            required
            placeholder="<EMAIL>"
          />
        </div>

        <div style={styles.inputGroup}>
          <label style={styles.label}>Mot de passe *</label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            style={styles.input}
            required
            placeholder="••••••••"
          />
        </div>

        <div style={styles.inputGroup}>
          <label style={styles.label}>Confirmer le mot de passe *</label>
          <input
            type="password"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            style={styles.input}
            required
            placeholder="••••••••"
          />
        </div>

        <div style={styles.inputGroup}>
          <label style={styles.label}>Spécialité *</label>
          <input
            type="text"
            name="specialite"
            value={formData.specialite}
            onChange={handleChange}
            style={styles.input}
            required
            placeholder="Ophtalmologie"
          />
        </div>

        <button 
          type="submit" 
          style={styles.button}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Inscription en cours...' : "S'inscrire"}
        </button>

        <p style={styles.loginLink}>
          Déjà inscrit ? <a href="/login-medecin" style={styles.link}>Se connecter</a>
        </p>
      </form>
    </div>
  );
};

export default MedecinRegister;
