import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../api';
import axios from 'axios';

const EditQuestion = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [texte, setTexte] = useState('');
  const [langue, setLangue] = useState('fr');
  const [category_id, setCategoryId] = useState('');
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    const fetchQuestion = async () => {
      try {
        const response = await api.get(`/admin/questions/${id}`);
        const question = response.data;
        setTexte(question.texte);
        setLangue(question.langue);
        setCategoryId(question.category_id);
      } catch (error) {
        console.error('Erreur lors de la récupération de la question', error);
      }
    };

    const fetchCategories = async () => {
      try {
        const response = await axios.get('http://localhost:8000/api/categories');
        setCategories(response.data);
      } catch (error) {
        console.error('Erreur lors du chargement des catégories', error);
      }
    };

    fetchQuestion();
    fetchCategories();
  }, [id]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await api.put(`/admin/questions/${id}`, { texte, langue, category_id });
      alert("Question modifiée avec succès");
      navigate('/questions');
    } catch (error) {
      console.error('Erreur lors de la modification de la question', error);
    }
  };

  return (
    <div className="container mt-4">
      <h2 className="text-primary mb-4">Modifier une Question</h2>
      <form onSubmit={handleSubmit} className="bg-light p-4 rounded shadow-sm">
        <div className="form-group mb-3">
          <label>Texte de la question :</label>
          <input
            type="text"
            className="form-control"
            value={texte}
            onChange={(e) => setTexte(e.target.value)}
            required
          />
        </div>

        <div className="form-group mb-3">
          <label>Langue :</label>
          <select
            value={langue}
            onChange={(e) => setLangue(e.target.value)}
            className="form-control"
          >
            <option value="fr">Français</option>
            <option value="en">Anglais</option>
            <option value="darija">Darija</option>
          </select>
        </div>

        <div className="form-group mb-4">
          <label>Catégorie :</label>
          <select
            className="form-control"
            value={category_id}
            onChange={(e) => setCategoryId(e.target.value)}
          >
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>
                {cat.name}
              </option>
            ))}
          </select>
        </div>

        <button type="submit" className="btn btn-success">Modifier</button>
      </form>
    </div>
  );
};

export default EditQuestion;
