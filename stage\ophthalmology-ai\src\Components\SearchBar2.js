import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchQuestions, fetchResponse } from '../actions/questionsActions';
import ConsultationRequest from './ConsultationRequest';
import {
  Search,
  MessageCircleQuestion,
  Stethoscope,
  Eye,
  Glasses,
  Eye as Vision,
  Sparkles,
  MessageCircle,
  Clock,
  TrendingUp,
  AlertCircle,
  X
} from 'lucide-react';

import '../styles/SearchBar2.css'


const SearchBar2 = () => {
  const dispatch = useDispatch();
  const [question, setQuestion] = useState('');
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  const [showDoctorForm, setShowDoctorForm] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showClearButton, setShowClearButton] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const searchInputRef = useRef(null);

  const response = useSelector((state) => state.questions.filteredQuestions);
  const questionsList = useSelector((state) => state.questions.questions);
  const loading = useSelector((state) => state.questions.loading);
  const error = useSelector((state) => state.questions.error);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [animateIcon, setAnimateIcon] = useState(false);

  const popularSuggestions = [
    { text: 'Je vois flou', icon: Vision },
    { text: 'J ai mal aux yeux', icon: AlertCircle },
    { text: 'Ma vision baisse', icon: TrendingUp },
    { text: 'J ai un point noir', icon: Eye },
    { text: 'Yeux secs', icon: Glasses },
  ];

  const randomQuestions = [
    "Pourquoi j’ai des taches dans la vision ?",
    "Est-ce normal de voir flou le soir ?",
    "Un enfant peut-il avoir un glaucome ?",
    "Quand consulter pour une cataracte ?"
  ];
  const [suggestionOfDay, setSuggestionOfDay] = useState('');

  useEffect(() => {
    dispatch(fetchQuestions());
    const random = randomQuestions[Math.floor(Math.random() * randomQuestions.length)];
    setSuggestionOfDay(random);

    // Charger les recherches récentes du localStorage
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
  }, [dispatch]);

  useEffect(() => {
    let debounceTimer;

    if (question.length > 1) {
      setIsTyping(true);
      debounceTimer = setTimeout(() => {
        const suggestions = questionsList.filter((q) =>
          q.texte.toLowerCase().includes(question.toLowerCase())
        );
        setFilteredSuggestions(suggestions);
        setIsTyping(false);
      }, 300);
    } else {
      setFilteredSuggestions([]);
    }

    setShowClearButton(question.length > 0);

    return () => clearTimeout(debounceTimer);
  }, [question, questionsList]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (!question.trim()) return;

    // Ajouter aux recherches récentes
    const newRecentSearches = [
      question,
      ...recentSearches.filter(search => search !== question)
    ].slice(0, 5);

    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));

    dispatch(fetchResponse(question));
    setShowModal(true);
  };

  const handleSuggestionClick = (suggestion) => {
    setQuestion(suggestion);
    dispatch(fetchResponse(suggestion));
    setShowModal(true);
    setFilteredSuggestions([]);
  };

  const clearSearch = () => {
    setQuestion('');
    setFilteredSuggestions([]);
    searchInputRef.current?.focus();
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  // Animation effect for the search icon
  useEffect(() => {
    if (question.length > 0) {
      setAnimateIcon(true);
      const timer = setTimeout(() => setAnimateIcon(false), 500);
      return () => clearTimeout(timer);
    }
  }, [question]);

  return (
    <div className="search-page">
      {/* Animated background shapes */}
      <div className="animated-shapes">
        <div className="shape shape-1"></div>
        <div className="shape shape-2"></div>
        <div className="shape shape-3"></div>
      </div>

      <div className={`search-hero ${isSearchFocused ? 'focused' : ''}`}>
        <div className="floating-icons">
          <Eye className="floating-icon icon-1" />
          <Glasses className="floating-icon icon-2" />
          <Vision className="floating-icon icon-3" />
        </div>

        <div className="search-content">
          <h1 className="search-title animate-title">
            Posez votre question ophtalmologique
            <span className="title-accent">24/7</span>
          </h1>

          <form onSubmit={handleSearch} className="search-form">
            <div className={`search-input-group ${animateIcon ? 'searching' : ''}`}>
              <input
                ref={searchInputRef}
                type="text"
                className="search-input"
                placeholder="Ex: Douleur à l'œil, vision floue..."
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                disabled={loading}
              />
              {showClearButton && (
                <button
                  type="button"
                  className="clear-button"
                  onClick={clearSearch}
                  disabled={loading}
                >
                  <X size={20} />
                </button>
              )}
              <button
                type="submit"
                className={`search-button ${question ? 'pulse' : ''} ${loading ? 'loading' : ''}`}
                disabled={!question.trim() || loading}
              >
                {loading ? (
                  <span className="loading-spinner"></span>
                ) : (
                  <Search className={`search-icon ${animateIcon ? 'rotate' : ''}`} />
                )}
                {loading ? 'Recherche...' : 'Rechercher'}
              </button>
            </div>

            {error && (
              <div className="search-error">
                <AlertCircle size={16} />
                {error}
                <button
                  className="clear-error"
                  onClick={() => dispatch({ type: 'SET_ERROR', payload: null })}
                >
                  <X size={14} />
                </button>
              </div>
            )}
          </form>

          {isTyping && !loading && (
            <div className="typing-indicator">
              Recherche en cours...
            </div>
          )}

          <div className="suggestions-container">
            {filteredSuggestions.length > 0 && (
              <div className="suggestions-dropdown fade-in">
                {filteredSuggestions.map((q, index) => (
                  <button
                    key={index}
                    className="suggestion-item ripple"
                    onClick={() => handleSuggestionClick(q.texte)}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <MessageCircle className="suggestion-icon" />
                    {q.texte}
                  </button>
                ))}
              </div>
            )}
          </div>

          {recentSearches.length > 0 && !filteredSuggestions.length && (
            <div className="recent-searches">
              <div className="recent-searches-header">
                <h5>
                  <Clock className="feature-icon" />
                  Recherches récentes
                </h5>
                <button
                  className="clear-recent"
                  onClick={clearRecentSearches}
                >
                  Effacer l'historique
                </button>
              </div>
              <div className="recent-searches-list">
                {recentSearches.map((search, idx) => (
                  <span
                    key={idx}
                    className="recent-search-item"
                    onClick={() => handleSuggestionClick(search)}
                  >
                    <Clock className="recent-icon" size={16} />
                    {search}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="search-features">
        <div className="features-grid">
          <div className="popular-suggestions feature-card">
            <h5>
              <Sparkles className="feature-icon" />
              Suggestions populaires
            </h5>
            <div className="suggestions-grid">
              {popularSuggestions.map((sugg, idx) => (
                <span
                  key={idx}
                  className="suggestion-pill hover-effect"
                  onClick={() => handleSuggestionClick(sugg.text)}
                >
                  <sugg.icon className="pill-icon" />
                  {sugg.text}
                </span>
              ))}
            </div>
          </div>

          {suggestionOfDay && (
            <div className="daily-suggestion feature-card glow-effect">
              <div className="suggestion-header">
                <MessageCircleQuestion className="suggestion-icon pulse" />
                <h5>Question du jour</h5>
              </div>
              <p className="suggestion-text">{suggestionOfDay}</p>
              <div className="suggestion-footer">
                <button
                  className="try-suggestion-btn"
                  onClick={() => handleSuggestionClick(suggestionOfDay)}
                >
                  Essayer cette question
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal avec animations améliorées */}
      {response && showModal && (
        <div className="modal fade show d-block custom-modal" tabIndex="-1">
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content animate-modal">
              <div className="modal-header">
                <h5 className="modal-title">Résultat de votre recherche</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    dispatch({ type: 'CLEAR_RESPONSE' });
                    setShowModal(false);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                {response.necessite_consultation ? (
                  <div className="consultation-message">
                    <h5 className="d-flex align-items-center mb-3">
                      <Stethoscope className="me-2" />
                      Conseil médical
                    </h5>
                    <p>{response.message}</p>
                    <p>Souhaitez-vous contacter un médecin ?</p>
                    <div className="d-flex gap-3">
                      <button
                        className="modal-action-button btn btn-success"
                        onClick={() => setShowDoctorForm(true)}
                      >
                        Oui
                      </button>
                      <button
                        className="modal-action-button btn btn-outline-secondary"
                        onClick={() => setShowDoctorForm(false)}
                      >
                        Non
                      </button>
                    </div>
                    {showDoctorForm && (
                      <div className="mt-4">
                        <ConsultationRequest specialite={response.specialite || 'Ophtalmologue'} />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="response-message">
                    {response.message}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  className="modal-action-button btn btn-outline-secondary"
                  onClick={() => {
                    dispatch({ type: 'CLEAR_RESPONSE' });
                    setShowModal(false);
                  }}
                >
                  Fermer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar2;
