<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Medecin;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;



class AuthController extends Controller
{
 
    
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required'
            ]);
    
            $medecin = Medecin::where('email', $request->email)->first();
    
            if (!$medecin || !Hash::check($request->password, $medecin->password)) {
                return response()->json(['message' => 'Email ou mot de passe incorrect'], 401);
            }
    
            $token = $medecin->createToken('medecinToken')->plainTextToken;
    
            return response()->json([
                'medecin' => $medecin,
                'token' => $token
            ]);
    
        } catch (\Exception $e) {
            Log::error('Erreur login : ' . $e->getMessage());
            return response()->json(['error' => 'Erreur serveur : ' . $e->getMessage()], 500);
        }
    }
    


public function register(Request $request)
{
    // Validation de l'email et du mot de passe
    $request->validate([
        'nom' => 'required|string|max:255',
        'email' => 'required|email|unique:medecins,email',
        'password' => 'required|min:6',
        'specialite' => 'required|string|max:255',
    ]);
    if ($request) {
        // Création de l'utilisateur
        $medecin = Medecin::create([
            'nom' => $request->nom,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'specialite' => $request->specialite,
        ]);
        

        // Retourner une réponse avec l'utilisateur ou un token d'authentification
        return response()->json($medecin, 201);
    }
    
    return response()->json(['errors' => $request->errors()], 422);
}



}
