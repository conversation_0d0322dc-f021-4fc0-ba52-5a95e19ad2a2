import axios from "axios";
import store from './store/store'; // Assure-toi que le chemin est correct

const API = axios.create({
  baseURL: "http://localhost:8000/api/",
});

// Vérifier si le token est défini avant de l'ajouter aux requêtes
API.interceptors.request.use((config) => {
  const state = store.getState();

  // Vérifier d'abord le token médecin dans localStorage
  const medecinToken = localStorage.getItem('medecinToken');

  if (medecinToken) {
    config.headers.Authorization = `Bearer ${medecinToken}`;
  } else if (state.user && state.user.token) {
    config.headers.Authorization = `Bearer ${state.user.token}`;
  }

  return config;
}, (error) => {
  return Promise.reject(error);
});

export default API;
