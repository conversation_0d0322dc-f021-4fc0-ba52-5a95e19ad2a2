import React, { useState, useEffect } from 'react';
import api from '../../api';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const AddQuestion = () => {
  const [texte, setTexte] = useState('');
  const [reponse, setReponse] = useState('');
  const [langue, setLangue] = useState('fr');
  const [category_id, setCategoryId] = useState('');
  const [categories, setCategories] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('http://localhost:8000/api/categories');
        setCategories(response.data);
      } catch (error) {
        console.error('Erreur lors du chargement des catégories', error);
      }
    };
    fetchCategories();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation des champs
    if (!texte.trim() || !reponse.trim() || !langue || !category_id) {
      alert('Tous les champs sont obligatoires');
      return;
    }

    const formData = {
      texte: texte.trim(),
      reponse: reponse.trim(),
      langue,
      category_id: parseInt(category_id),
      patient_id: 1
    };

    console.log('Données envoyées:', formData);

    try {
      console.log('Début de la requête API');
      const response = await api.post('/admin/questions', formData);
      console.log('Réponse du serveur:', response);

      if (response.data) {
        console.log('Données reçues:', response.data);
        alert('Question et réponse ajoutées avec succès');
        navigate('/questions');
      }
    } catch (error) {
      console.log('Headers de la requête:', error.config?.headers);
      console.log('URL de la requête:', error.config?.url);
      console.log('Données de la requête:', error.config?.data);
      console.error('Erreur complète:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      alert(`Erreur lors de l'ajout: ${error.response?.data?.message || error.message}`);
    }
  };

  return (
    <div className="container mt-5">
      <div className="card shadow-lg mx-auto" style={{ maxWidth: '700px' }}>
        <div className="card-header text-center">
          <h4 className="mb-0" style={{ color: '#006fac' }}>Ajouter une Question</h4>
        </div>
        <div className="card-body">
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label className="form-label">Question :</label>
              <input
                type="text"
                value={texte}
                onChange={(e) => setTexte(e.target.value)}
                className="form-control"
                placeholder="Ex. J'ai des douleurs à l'œil..."
                required
              />
            </div>

            <div className="mb-3">
              <label className="form-label">Réponse :</label>
              <textarea
                value={reponse}
                onChange={(e) => setReponse(e.target.value)}
                className="form-control"
                rows="4"
                placeholder="Entrez la réponse du médecin..."
                required
              />
            </div>

            <div className="mb-3">
              <label className="form-label">Langue :</label>
              <select
                value={langue}
                onChange={(e) => setLangue(e.target.value)}
                className="form-select"
                required
              >
                <option value="fr">Français</option>
                <option value="en">Anglais</option>
                <option value="darija">Darija</option>
              </select>
            </div>

            <div className="mb-4">
              <label className="form-label">Catégorie :</label>
              <select
                value={category_id}
                onChange={(e) => setCategoryId(e.target.value)}
                className="form-select"
                required
              >
                <option value="">-- Sélectionner une catégorie --</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="text-center">
              <button type="submit" className="btn btn-success px-4" style={{ backgroundColor: '#74d8ff' }}>
                Ajouter
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddQuestion;







