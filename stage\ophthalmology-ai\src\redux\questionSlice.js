
// import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
// import API from "../api";

// // Action asynchrone pour récupérer la réponse à une question
// export const fetchResponse = createAsyncThunk(
//   "question/fetchResponse",
//   async (query, { rejectWithValue }) => {
//     try {
//       console.log("envoi de la requete api avec : " , query)
//       const response = await API.get(`/questions/search?query=${query}`);
//       return response.data; // { reponse, audio_url }
//     } catch (error) {
//       return rejectWithValue(error.response?.data || { message: "Erreur inconnue" });
//     }
//   }
// );
// // pour envoyer la demande de consultation 


// export const requestConsultation = createAsyncThunk(
//   "question/requestConsultation",
//   async ({ motif, date }, { rejectWithValue }) => {
//     try {
//       const response = await fetch("/api/consultation", {
//         method: "POST",
//         headers: { "Content-Type": "application/json" },
//         body: JSON.stringify({ motif, date, patient_id: 1, medecin_id: 1 }),
//       });
//       return await response.json();
//     } catch (error) {
//       return rejectWithValue(error.message);
//     }
//   }
// );



// const responseSlice = createSlice({
//   name: "question",
//   initialState: {
//     reponse: null,
//     audio_url: null,
//     necessite_consultation: false,
//     loading: false,
//     error: null,
//   },
//   reducers: {
//     startLoading: (state) => {
//       state.loading = true;
//       state.error = null;
//     },
//     setResponse: (state, action) => {
//       state.loading = false;
//       state.reponse = action.payload.reponse;
//       state.audio_url = action.payload.audio_url;
//       state.necessite_consultation = action.payload.necessite_consultation;
//     },
//     setError: (state, action) => {
//       state.loading = false;
//       state.error = action.payload;
//     },
//     resetState: (state) => {
//       state.reponse = null;
//       state.audio_url = null;
//       state.necessite_consultation = false;
//       state.loading = false;
//       state.error = null;
//     }
//   }
// });

// export const { startLoading, setResponse, setError, resetState } = responseSlice.actions;

// export default responseSlice.reducer;
