<?php

namespace App\Http\Controllers;
use App\Models\Question;

use Illuminate\Http\Request;

class CategoryController
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(\App\Models\Category::all());
    }
    
    public function questionsFrequentes($id)
{
    $questions = Question::where('categorie_id', $id)
                         ->orderBy('frequence', 'desc')
                         ->take(5)
                         ->get();
    return response()->json($questions);
}


    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
