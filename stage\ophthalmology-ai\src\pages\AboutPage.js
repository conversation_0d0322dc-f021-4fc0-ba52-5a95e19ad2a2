import React, { useState, useEffect } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import { FaStar, FaRegStar } from 'react-icons/fa';
import ophtaImage from "../assets/ophta1.jpg";

export default function AboutUs() {
  const [commentaires, setCommentaires] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [newRating, setNewRating] = useState(0);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [patientName, setPatientName] = useState('');


  // Charger les commentaires depuis le backend
  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/comments');
        if (!response.ok) throw new Error(`Erreur de récupération des commentaires: ${response.statusText}`);
        const data = await response.json();
        setCommentaires(data);
      } catch (error) {
        setError(error.message);
      }
    };

    fetchComments();
  }, []);

  // Fonction pour ajouter un nouveau commentaire
  const handleCommentSubmit = async () => {
    if (newComment && newRating) {
      const commentData = {
        text: newComment,
        rating: newRating,
        patient_name: patientName || "Anonyme", 
      };

      try {
        const response = await fetch('http://localhost:8000/api/comments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(commentData),
        });

        const result = await response.json();

        if (response.ok) {
          // Ajouter le nouveau commentaire dans l'état
          setCommentaires((prevComments) => [
            ...prevComments,
            result, // Nouveau commentaire retourné par le backend
          ]);
          setNewComment('');
          setNewRating(0);
          setMessage('Commentaire ajouté avec succès !');
        } else {
          console.error('Erreur lors de l\'enregistrement du commentaire', result);
          setError('Erreur lors de l\'enregistrement du commentaire');
        }
      } catch (error) {
        console.error('Erreur réseau', error);
        setError('Erreur réseau');
      }
    } else {
      setError('Le commentaire et la note sont obligatoires');
    }
  };

  return (
    <div className="container mt-5">
      <div className="row align-items-center mb-5">
        <div className="col-md-6">
          <img src={ophtaImage} alt="Ophtalmologie" className="img-fluid rounded shadow" />
        </div>
        <div className="col-md-6">
          <h2>À Propos de Nous</h2>
          <p className="lead mt-3">
            OphtalmoAI est une plateforme innovante qui permet aux patients de poser des questions sur l'ophtalmologie et d'obtenir des réponses fiables et pertinentes.
          </p>
          <p>
            Notre mission est de rendre l'information médicale plus accessible grâce à l'intelligence artificielle et d'améliorer la prise en charge des patients souffrant de troubles de la vision.
          </p>
          <p>
            Nous travaillons avec des professionnels de santé pour garantir des réponses précises et actualisées.
          </p>
        </div>
      </div>

      <h2 className="text-center mb-4">Ce que disent nos patients</h2>
      <div className="row text-center">
        
        {commentaires.length > 0 ? (
          commentaires.map((comment, index) => (
            <div key={index} className="col-md-4">
              <div className="card p-3 shadow-sm">
                <p className="card-text">"{comment.text}"</p>
                <div className="stars">
                  {[...Array(5)].map((_, i) => (
                    <i key={i}>
                      {i < comment.rating ? <FaStar /> : <FaRegStar />}
                    </i>
                  ))}
                </div>
                <h5 className="mt-3">- {comment.patient_name}</h5>
              </div>
            </div>
          ))
        ) : (
          <div className="col-md-12">
            <p>Aucun commentaire pour le moment.</p>
          </div>
        )}
      </div>
      <div className="mt-3">
  <label>Nom du patient :</label>
  <input
    type="text"
    className="form-control"
    value={patientName}
    onChange={(e) => setPatientName(e.target.value)}
    placeholder="Votre nom"
  />
</div>


      <div className="mt-5">
        <h3>Laissez un commentaire</h3>
        <textarea
          className="form-control"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          rows="3"
          placeholder="Votre commentaire"
        ></textarea>
        <div className="mt-3">
          <label>Évaluation :</label>
          <select
            className="form-control"
            value={newRating}
            onChange={(e) => setNewRating(Number(e.target.value))}
          >
            <option value="0">Choisir une note</option>
            {[1, 2, 3, 4, 5].map((rating) => (
              <option key={rating} value={rating}>{rating} étoile{rating > 1 ? 's' : ''}</option>
            ))}
          </select>
        </div>
        <button className="btn btn-primary mt-3" onClick={handleCommentSubmit}>
          Soumettre
        </button>
        {message && <p className="text-success mt-3">{message}</p>}
        {error && <p className="text-danger mt-3">{error}</p>}
      </div>
    </div>
  );
}
