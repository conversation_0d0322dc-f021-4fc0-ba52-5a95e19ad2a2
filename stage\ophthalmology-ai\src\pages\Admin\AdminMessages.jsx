// import React, { useEffect, useState } from "react";
// import axios from "axios";

// const AdminMessages = () => {
//   const [messages, setMessages] = useState([]);

//   useEffect(() => {
//     axios.get("http://localhost:8000/api/contacts").then((res) => {
//       setMessages(res.data);
//     });
//   }, []);

//   return (
//     <div>
//       <h2 className="text-xl font-bold mb-4">Messages reçus</h2>
//       {messages.map((msg) => (
//         <div key={msg.id} className="bg-white p-4 rounded shadow mb-3">
//           <p><strong>👤 {msg.name}</strong> ({msg.email})</p>
//           <p className="text-sm text-gray-600">📱 {msg.phone}</p>
//           <p className="mt-2">💬 {msg.message}</p>
//         </div>
//       ))}
//     </div>
//   );
// };

// export default AdminMessages;
