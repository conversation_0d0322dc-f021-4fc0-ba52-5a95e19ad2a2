{"name": "ophthalmology-ai", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "franc-min": "^6.2.0", "fuse.js": "^7.1.0", "jquery": "^3.7.1", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-autosuggest": "^10.1.0", "react-bootstrap-icons": "^1.11.5", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "redux-thunk": "^3.1.0", "select2": "^4.1.0-rc.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}