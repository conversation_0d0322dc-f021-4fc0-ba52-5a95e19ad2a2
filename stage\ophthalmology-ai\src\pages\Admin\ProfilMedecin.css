.profile-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 30px;
  max-width: 900px;
  margin: 30px auto;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.profile-header h2 {
  color: #006fac;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #e6f7ff;
  color: #006fac;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-button:hover {
  background-color: #b5e8ff;
  transform: translateY(-2px);
}

.profile-photo-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.profile-photo {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f8ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #006fac;
  border: 3px solid #e6f7ff;
}

.profile-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-upload {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 111, 172, 0.8);
  padding: 5px;
  text-align: center;
}

.upload-button {
  color: white;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.profile-name h3 {
  margin: 0 0 5px;
  font-size: 20px;
  color: #2d3748;
}

.profile-name p {
  margin: 0;
  color: #718096;
  font-size: 14px;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.profile-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-field.full-width {
  grid-column: span 2;
}

.profile-field label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a5568;
  font-weight: 500;
  font-size: 14px;
}

.profile-field input,
.profile-field textarea {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.profile-field input:focus,
.profile-field textarea:focus {
  border-color: #006fac;
  box-shadow: 0 0 0 3px rgba(0, 111, 172, 0.1);
  outline: none;
}

.profile-field input:read-only,
.profile-field textarea:read-only {
  background-color: #f8fafc;
  cursor: default;
}

.profile-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.cancel-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff3f3;
  color: #ff4757;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  background-color: #ffe0e0;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(to right, #006fac, #00aff8);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 111, 172, 0.2);
}

.save-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.profile-error {
  background-color: #fff3f3;
  border-left: 4px solid #ff4757;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-error p {
  margin: 0;
  color: #ff4757;
}

.profile-error button {
  background: none;
  border: none;
  color: #ff4757;
  font-size: 18px;
  cursor: pointer;
}

.profile-success {
  background-color: #f0fff4;
  border-left: 4px solid #38a169;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 8px;
  animation: fadeIn 0.3s ease-out;
}

.profile-success p {
  margin: 0;
  color: #38a169;
}

.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #006fac;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
  
  .profile-field.full-width {
    grid-column: span 1;
  }
  
  .profile-photo-section {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-actions {
    flex-direction: column-reverse;
    gap: 10px;
  }
  
  .save-button, .cancel-button {
    width: 100%;
    justify-content: center;
  }
}
