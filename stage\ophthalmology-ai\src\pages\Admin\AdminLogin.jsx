// import React, { useState } from 'react';
// import axios from 'axios';

// const AdminLogin = ({ onLogin }) => {
//   const [email, setEmail] = useState('');
//   const [password, setPassword] = useState('');
//   const [error, setError] = useState('');

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     try {
//       const res = await axios.post('http://localhost:8000/api/admin/login', {
//         email,
//         password,
//       });
//       localStorage.setItem('adminToken', res.data.token);
//       onLogin(res.data.admin);
//     } catch (err) {
//       setError('Email ou mot de passe incorrect');
//     }
//   };

//   return (
//     <div className="max-w-md mx-auto mt-20 bg-white p-8 shadow rounded-xl">
//       <h2 className="text-2xl font-bold mb-4">Connexion Admin</h2>
//       {error && <p className="text-red-500">{error}</p>}
//       <form onSubmit={handleSubmit}>
//         <input type="email" placeholder="Email"
//           className="w-full border p-2 mb-3"
//           value={email} onChange={(e) => setEmail(e.target.value)} />
//         <input type="password" placeholder="Mot de passe"
//           className="w-full border p-2 mb-3"
//           value={password} onChange={(e) => setPassword(e.target.value)} />
//         <button type="submit" className="w-full bg-blue-600 text-white p-2 rounded">Se connecter</button>
//       </form>
//     </div>
//   );
// };

// export default AdminLogin;
