import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    const fetchNotifications = async () => {
  const token = localStorage.getItem('medecinToken');
  const medecinId = localStorage.getItem('medecinId'); // ⚠️ Stocke l'ID du médecin au login
  const res = await axios.get(`http://localhost:8000/api/medecins/${medecinId}/notifications`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  setNotifications(res.data);
};

    fetchNotifications();
  }, []);

  return (
    <div>
      <h3>Notifications</h3>
      <ul>
        {notifications.map((n) => (
          <li key={n.id}>
            <strong>{n.titre}</strong> - {n.contenu} {n.lu ? "(Lu)" : "(Non lu)"}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Notifications;
