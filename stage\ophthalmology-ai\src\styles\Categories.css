.categories-section {
  background: linear-gradient(135deg, #eff9ff 0%, #ffffff 100%);
  padding: 4rem 0;
  min-height: 100vh;
}

.categories-title {
  color: #006fac;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.categories-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 2px;
}

.category-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 111, 172, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 111, 172, 0.2);
}

.category-img-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.category-card:hover .category-img {
  transform: scale(1.1);
}

.category-content {
  padding: 1.5rem;
}

.category-name {
  color: #003d5c;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.category-description {
  color: #054e75;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: center;
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 111, 172, 0.8), rgba(0, 139, 213, 0.9));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 2rem;
}

.category-card:hover .category-overlay {
  opacity: 1;
}

.category-overlay-text {
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
}

.category-btn {
  background: white;
  color: #006fac;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.category-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Animation d'apparition */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-card {
  animation: fadeInUp 0.6s ease forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .categories-section {
    padding: 2rem 0;
  }

  .categories-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .category-img-container {
    height: 200px;
  }

  .category-content {
    padding: 1rem;
  }

  .category-name {
    font-size: 1.2rem;
  }

  .category-description {
    font-size: 0.9rem;
  }
}