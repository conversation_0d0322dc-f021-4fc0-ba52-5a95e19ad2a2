import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import 'bootstrap/dist/css/bootstrap.min.css';
import { Eye, Home, Layers, Info, Phone, UserCircle2 } from "lucide-react";
import NotificationBell from "./NotificationBell";

function Navbar() {
  const location = useLocation();
  const [isAdmin, setIsAdmin] = useState(false);

  // Vérifier si l'utilisateur est un administrateur ou un médecin
  useEffect(() => {
    const adminId = localStorage.getItem('adminId');
    const medecinId = localStorage.getItem('medecinId');

    // Si l'utilisateur a un ID admin, il est considéré comme admin
    // Sinon, s'il a un ID médecin, il est considéré comme médecin
    setIsAdmin(!!adminId);
  }, []);

  const navLinkStyle = (path) => ({
    color: location.pathname === path ? "#005e8e" : "#006fac",
    fontWeight: location.pathname === path ? "600" : "500",
    padding: "0.5rem 1rem",
    textDecoration: "none",
    display: "flex",
    alignItems: "center",
    backgroundColor: location.pathname === path ? "#e0f2fe" : "transparent",
  });

  const buttonStyle = {
    backgroundColor: "#006fac",
    borderColor: "#006fac",
    padding: "0.5rem 1.2rem",
    borderRadius: "4px",
    color: "white",
    display: "flex",
    alignItems: "center",
    gap: "0.5rem",
    fontWeight: "500",
  };

  return (
    <nav className="navbar navbar-expand-lg shadow-sm sticky-top bg-white">
      <div className="container-fluid px-4">
        <Link className="navbar-brand d-flex align-items-center" to="/">
          <Eye className="me-2" size={32} style={{ color: "#006fac" }} />
          <span className="fw-bold fs-4" style={{ color: "#005e8e" }}>OphtalmoAI</span>
        </Link>

        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav align-items-center">
            <li className="nav-item">
              <Link className="nav-link" to="/" style={navLinkStyle("/")}>
                <Home size={20} className="me-2" />
                Accueil
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/categories" style={navLinkStyle("/categories")}>
                <Layers size={20} className="me-2" />
                Catégories
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/a-propos" style={navLinkStyle("/a-propos")}>
                <Info size={20} className="me-2" />
                À propos
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link" to="/contact" style={navLinkStyle("/contact")}>
                <Phone size={20} className="me-2" />
                Contact
              </Link>
            </li>
          </ul>
          <div className="ms-auto d-flex align-items-center">
            {/* Afficher la cloche de notification uniquement dans le dashboard médecin/admin */}
            {location.pathname.startsWith('/medecin') && (
              <div className="me-3">
                <NotificationBell isAdmin={isAdmin} />
              </div>
            )}

            {/* Afficher le bouton Espace Médecin uniquement si l'utilisateur n'est pas déjà connecté */}
            {!location.pathname.startsWith('/medecin') && (
              <Link
                to="/login-medecin"
                className="btn"
                style={buttonStyle}
              >
                <UserCircle2 size={20} />
                Espace Médecin
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
