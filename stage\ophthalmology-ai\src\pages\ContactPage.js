import React, { useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../styles/Contact.css';
import { MapPin, Phone, Mail } from 'lucide-react';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [successMessage, setSuccessMessage] = useState('');

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [id]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (formData.name && formData.email && formData.message) {
      // Ici, vous pouvez ajouter l'appel API pour envoyer le message
      setSuccessMessage('✅ Votre message a été envoyé avec succès !');
      setFormData({ name: '', email: '', message: '' });
    } else {
      setSuccessMessage('❌ Veuillez remplir tous les champs.');
    }
  };

  return (
    <section className="contact-section">
      <div className="container">
        <h2 className="contact-title">Contactez-nous</h2>

        <div className="row">
          <div className="col-lg-6">
            <div className="contact-form">
              <form onSubmit={handleSubmit}>
                <div className="mb-3">
                  <label htmlFor="name" className="form-label">Nom</label>
                  <input
                    type="text"
                    className="form-control"
                    id="name"
                    placeholder="Votre nom"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    placeholder="Votre email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="message" className="form-label">Message</label>
                  <textarea
                    className="form-control"
                    id="message"
                    rows="4"
                    placeholder="Votre message"
                    value={formData.message}
                    onChange={handleChange}
                  ></textarea>
                </div>
                <button type="submit" className="contact-btn">
                  Envoyer le message
                </button>
              </form>
              {successMessage && (
                <div className={`alert ${successMessage.includes('✅') ? 'alert-success' : 'alert-error'}`}>
                  {successMessage}
                </div>
              )}
            </div>
          </div>

          <div className="col-lg-6">
            <div className="contact-info">
              <h4>Nos coordonnées</h4>
              <p>
                <MapPin className="me-2" size={20} />
                <strong>Adresse :</strong> Avenue Allal El Fassi, Madinat Al Irfane, Hay Riad, Rabat
              </p>
              <p>
                <Phone className="me-2" size={20} />
                <strong>Téléphone :</strong> +212 (05) 37 13 14 00
              </p>
              <p>
                <Mail className="me-2" size={20} />
                <strong>Email :</strong> <EMAIL>
              </p>

              <div className="map-container">
                <iframe
                  title="Hopital Cheikh Zayed Rabat"
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3318.925349580166!2d-6.888474584793979!3d33.99773722839456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xda76cd7c5f5b3f3%3A0xd33f35f9b6cd1cd5!2sH%C3%B4pital%20Universitaire%20International%20Cheikh%20Zaid!5e0!3m2!1sfr!2sma!4v1648003708207!5m2!1sfr!2sma"
                  width="100%"
                  height="300"
                  style={{ border: 0 }}
                  allowFullScreen=""
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade">
                </iframe>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
