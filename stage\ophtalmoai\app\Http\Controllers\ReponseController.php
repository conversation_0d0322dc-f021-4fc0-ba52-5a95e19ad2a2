<?php

namespace App\Http\Controllers;

use App\Models\Reponse;
use App\Models\Notification;

use Illuminate\Http\Request; 

class ReponseController extends Controller 
{
    public function index()
    { 
        return response()->json(Reponse::all());
    }

    // public function store(Request $request)
    // {
    //     $reponse = Reponse::create($request->all());
    //     return response()->json($reponse, 201);
    // }
    public function store(Request $request)
    {
        $request->validate([
            'texte' => 'nullable|string',
            'audio_url' => 'nullable|string',
            'medecin_id' => 'required|exists:medecins,id',
            'question_id' => 'required|exists:questions,id',
        ]);
    
        $reponse = Reponse::create([
            'texte' => $request->texte,
            'audio_url' => $request->audio_url,
            'medecin_id' => $request->medecin_id,
            'question_id' => $request->question_id,
        ]);
    
        // Envoie de notification au patient
        $patient = $reponse->question->patient;
        $patient->notify(new Notification($reponse));
    
        return response()->json($reponse, 201);
    }
    
public function update(Request $request, $id)
{
    $reponse = Reponse::findOrFail($id);

    $request->validate([
        'texte' => 'nullable|string',
        'audio_url' => 'nullable|string',
    ]);

    $reponse->update([
        'texte' => $request->texte,
        'audio_url' => $request->audio_url,
    ]);

    return response()->json($reponse); // Retourne la réponse mise à jour
}
public function destroy($id)
{
    $reponse = Reponse::findOrFail($id);
    $reponse->delete();

    return response()->json(['message' => 'Réponse supprimée avec succès.']);
}

} 