{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\stage\\\\ophthalmology-ai\\\\src\\\\pages\\\\Admin\\\\QuestionsManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport api from \"../../api\";\nimport { Link } from \"react-router-dom\";\nimport { PencilSquare, Trash, Eye } from 'react-bootstrap-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionsManagement = () => {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [error, setError] = useState(null);\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(10);\n  useEffect(() => {\n    fetchQuestions();\n  }, []);\n  const fetchQuestions = async () => {\n    try {\n      setLoading(true);\n\n      // Vérifier le token avant la requête\n      const token = localStorage.getItem('medecinToken');\n      console.log('Token médecin:', token ? 'Présent' : 'Absent');\n      const response = await api.get('/admin/questions');\n      console.log('Questions reçues:', response.data); // Pour déboguer\n      setQuestions(response.data);\n      setError(null);\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response5, _error$response6, _error$response6$data;\n      console.error('Erreur complète:', error);\n      console.error('Status:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status);\n      console.error('Data:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n      let errorMessage = 'Erreur lors de la récupération des questions';\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 401) {\n        errorMessage = 'Non autorisé. Veuillez vous reconnecter.';\n      } else if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 403) {\n        errorMessage = 'Accès refusé. Privilèges administrateur requis.';\n      } else if (((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 404) {\n        errorMessage = 'Route non trouvée. Vérifiez la configuration.';\n      } else if ((_error$response6 = error.response) !== null && _error$response6 !== void 0 && (_error$response6$data = _error$response6.data) !== null && _error$response6$data !== void 0 && _error$response6$data.message) {\n        errorMessage = error.response.data.message;\n      }\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {\n      try {\n        await api.delete(`/admin/questions/${id}`);\n        setQuestions(questions.filter(q => q.id !== id));\n        alert(\"Question supprimée avec succès\");\n      } catch (error) {\n        alert(\"Erreur lors de la suppression de la question\");\n        console.error(\"Erreur lors de la suppression de la question\", error);\n      }\n    }\n  };\n  const filteredQuestions = questions.filter(question => {\n    var _question$categorie;\n    return question.texte.toLowerCase().includes(searchTerm.toLowerCase()) || (((_question$categorie = question.categorie) === null || _question$categorie === void 0 ? void 0 : _question$categorie.nom) || '').toLowerCase().includes(searchTerm.toLowerCase());\n  });\n\n  // Pagination logic\n  const indexOfLastQuestion = currentPage * questionsPerPage;\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\n  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);\n  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Generate page numbers\n  const pageNumbers = [];\n  for (let i = 1; i <= totalPages; i++) {\n    pageNumbers.push(i);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-4 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Chargement...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Gestion des Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/questions/ajouter\",\n        className: \"btn btn-primary\",\n        children: \"+ Ajouter une question\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card shadow-sm mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Rechercher une question ou une cat\\xE9gorie...\",\n            value: searchTerm,\n            onChange: e => {\n              setSearchTerm(e.target.value);\n              setCurrentPage(1); // Reset to first page on search\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table table-hover table-bordered align-middle\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"table-primary text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Question\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"R\\xE9ponse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: currentQuestions.length > 0 ? currentQuestions.map(question => {\n                var _question$reponse, _question$categorie2;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-center\",\n                    children: question.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: question.texte\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_question$reponse = question.reponse) === null || _question$reponse === void 0 ? void 0 : _question$reponse.texte) || /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Non d\\xE9finie\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_question$categorie2 = question.categorie) === null || _question$categorie2 === void 0 ? void 0 : _question$categorie2.nom) || /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Aucune\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: `/questions/details/${question.id}`,\n                      className: \"btn btn-sm btn-outline-info mx-1\",\n                      title: \"Voir les d\\xE9tails\",\n                      children: /*#__PURE__*/_jsxDEV(Eye, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/questions/modifier/${question.id}`,\n                      className: \"btn btn-sm btn-outline-warning mx-1\",\n                      title: \"Modifier\",\n                      children: /*#__PURE__*/_jsxDEV(PencilSquare, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-danger mx-1\",\n                      onClick: () => handleDelete(question.id),\n                      title: \"Supprimer\",\n                      children: /*#__PURE__*/_jsxDEV(Trash, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 170,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this)]\n                }, question.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"5\",\n                  className: \"text-center text-muted\",\n                  children: searchTerm ? \"Aucun résultat trouvé\" : \"Aucune question disponible\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), filteredQuestions.length > questionsPerPage && /*#__PURE__*/_jsxDEV(\"nav\", {\n          \"aria-label\": \"Questions pagination\",\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"pagination justify-content-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === 1 ? 'disabled' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(currentPage - 1),\n                disabled: currentPage === 1,\n                children: \"Pr\\xE9c\\xE9dent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === number ? 'active' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(number),\n                children: number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)\n            }, number, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === totalPages ? 'disabled' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(currentPage + 1),\n                disabled: currentPage === totalPages,\n                children: \"Suivant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Affichage de \", indexOfFirstQuestion + 1, \" \\xE0 \", Math.min(indexOfLastQuestion, filteredQuestions.length), \" sur \", filteredQuestions.length, \" questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionsManagement, \"MhonT3Ej58OhvPsqS1VklmgNosI=\");\n_c = QuestionsManagement;\nexport default QuestionsManagement;\nvar _c;\n$RefreshReg$(_c, \"QuestionsManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "Link", "PencilSquare", "Trash", "Eye", "jsxDEV", "_jsxDEV", "QuestionsManagement", "_s", "questions", "setQuestions", "loading", "setLoading", "searchTerm", "setSearchTerm", "error", "setError", "currentPage", "setCurrentPage", "questionsPerPage", "fetchQuestions", "token", "localStorage", "getItem", "console", "log", "response", "get", "data", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "_error$response6", "_error$response6$data", "status", "errorMessage", "message", "handleDelete", "id", "window", "confirm", "delete", "filter", "q", "alert", "filteredQuestions", "question", "_question$categorie", "texte", "toLowerCase", "includes", "categorie", "nom", "indexOfLastQuestion", "indexOfFirstQuestion", "currentQuestions", "slice", "totalPages", "Math", "ceil", "length", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "e", "target", "map", "_question$reponse", "_question$categorie2", "reponse", "title", "onClick", "colSpan", "disabled", "number", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/stage/ophthalmology-ai/src/pages/Admin/QuestionsManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport api from \"../../api\";\r\nimport { <PERSON> } from \"react-router-dom\";\r\nimport { PencilSquare, Trash, Eye } from 'react-bootstrap-icons';\r\n\r\nconst QuestionsManagement = () => {\r\n  const [questions, setQuestions] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Pagination states\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [questionsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchQuestions();\r\n  }, []);\r\n\r\n  const fetchQuestions = async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Vérifier le token avant la requête\r\n      const token = localStorage.getItem('medecinToken');\r\n      console.log('Token médecin:', token ? 'Présent' : 'Absent');\r\n\r\n      const response = await api.get('/admin/questions');\r\n      console.log('Questions reçues:', response.data); // Pour déboguer\r\n      setQuestions(response.data);\r\n      setError(null);\r\n    } catch (error) {\r\n      console.error('Erreur complète:', error);\r\n      console.error('Status:', error.response?.status);\r\n      console.error('Data:', error.response?.data);\r\n\r\n      let errorMessage = 'Erreur lors de la récupération des questions';\r\n\r\n      if (error.response?.status === 401) {\r\n        errorMessage = 'Non autorisé. Veuillez vous reconnecter.';\r\n      } else if (error.response?.status === 403) {\r\n        errorMessage = 'Accès refusé. Privilèges administrateur requis.';\r\n      } else if (error.response?.status === 404) {\r\n        errorMessage = 'Route non trouvée. Vérifiez la configuration.';\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n      }\r\n\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {\r\n      try {\r\n        await api.delete(`/admin/questions/${id}`);\r\n        setQuestions(questions.filter((q) => q.id !== id));\r\n        alert(\"Question supprimée avec succès\");\r\n      } catch (error) {\r\n        alert(\"Erreur lors de la suppression de la question\");\r\n        console.error(\"Erreur lors de la suppression de la question\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const filteredQuestions = questions.filter(question =>\r\n    question.texte.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (question.categorie?.nom || '').toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  // Pagination logic\r\n  const indexOfLastQuestion = currentPage * questionsPerPage;\r\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\r\n  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);\r\n  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);\r\n\r\n  const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n  // Generate page numbers\r\n  const pageNumbers = [];\r\n  for (let i = 1; i <= totalPages; i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"container mt-4 text-center\">\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Chargement...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"container mt-4\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          {error}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mt-4\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h2>Gestion des Questions</h2>\r\n        <Link to=\"/questions/ajouter\" className=\"btn btn-primary\">\r\n          + Ajouter une question\r\n        </Link>\r\n      </div>\r\n\r\n      <div className=\"card shadow-sm mb-4\">\r\n        <div className=\"card-body\">\r\n          <div className=\"mb-3\">\r\n            <input\r\n              type=\"text\"\r\n              className=\"form-control\"\r\n              placeholder=\"Rechercher une question ou une catégorie...\"\r\n              value={searchTerm}\r\n              onChange={(e) => {\r\n                setSearchTerm(e.target.value);\r\n                setCurrentPage(1); // Reset to first page on search\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"table-responsive\">\r\n            <table className=\"table table-hover table-bordered align-middle\">\r\n              <thead className=\"table-primary text-center\">\r\n                <tr>\r\n                  <th>ID</th>\r\n                  <th>Question</th>\r\n                  <th>Réponse</th>\r\n                  <th>Catégorie</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentQuestions.length > 0 ? (\r\n                  currentQuestions.map((question) => (\r\n                    <tr key={question.id}>\r\n                      <td className=\"text-center\">{question.id}</td>\r\n                      <td>{question.texte}</td>\r\n                      <td>{question.reponse?.texte || <span className=\"text-muted\">Non définie</span>}</td>\r\n                      <td>{question.categorie?.nom || <span className=\"text-muted\">Aucune</span>}</td>\r\n                      <td className=\"text-center\">\r\n                        <Link\r\n                          to={`/questions/details/${question.id}`}\r\n                          className=\"btn btn-sm btn-outline-info mx-1\"\r\n                          title=\"Voir les détails\"\r\n                        >\r\n                          <Eye />\r\n                        </Link>\r\n                        <Link\r\n                          to={`/questions/modifier/${question.id}`}\r\n                          className=\"btn btn-sm btn-outline-warning mx-1\"\r\n                          title=\"Modifier\"\r\n                        >\r\n                          <PencilSquare />\r\n                        </Link>\r\n                        <button\r\n                          className=\"btn btn-sm btn-outline-danger mx-1\"\r\n                          onClick={() => handleDelete(question.id)}\r\n                          title=\"Supprimer\"\r\n                        >\r\n                          <Trash />\r\n                        </button>\r\n                      </td>\r\n                    </tr>\r\n                  ))\r\n                ) : (\r\n                  <tr>\r\n                    <td colSpan=\"5\" className=\"text-center text-muted\">\r\n                      {searchTerm ? \"Aucun résultat trouvé\" : \"Aucune question disponible\"}\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          {/* Pagination */}\r\n          {filteredQuestions.length > questionsPerPage && (\r\n            <nav aria-label=\"Questions pagination\" className=\"mt-4\">\r\n              <ul className=\"pagination justify-content-center\">\r\n                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\r\n                  <button\r\n                    className=\"page-link\"\r\n                    onClick={() => paginate(currentPage - 1)}\r\n                    disabled={currentPage === 1}\r\n                  >\r\n                    Précédent\r\n                  </button>\r\n                </li>\r\n                \r\n                {pageNumbers.map(number => (\r\n                  <li key={number} className={`page-item ${currentPage === number ? 'active' : ''}`}>\r\n                    <button\r\n                      className=\"page-link\"\r\n                      onClick={() => paginate(number)}\r\n                    >\r\n                      {number}\r\n                    </button>\r\n                  </li>\r\n                ))}\r\n                \r\n                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\r\n                  <button\r\n                    className=\"page-link\"\r\n                    onClick={() => paginate(currentPage + 1)}\r\n                    disabled={currentPage === totalPages}\r\n                  >\r\n                    Suivant\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n              <div className=\"text-center mt-2\">\r\n                <small className=\"text-muted\">\r\n                  Affichage de {indexOfFirstQuestion + 1} à {Math.min(indexOfLastQuestion, filteredQuestions.length)} sur {filteredQuestions.length} questions\r\n                </small>\r\n              </div>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsManagement;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,YAAY,EAAEC,KAAK,EAAEC,GAAG,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqB,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAEvCC,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAClDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,KAAK,GAAG,SAAS,GAAG,QAAQ,CAAC;MAE3D,MAAMK,QAAQ,GAAG,MAAM1B,GAAG,CAAC2B,GAAG,CAAC,kBAAkB,CAAC;MAClDH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjDlB,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAAC;MAC3BZ,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA,IAAAc,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdX,OAAO,CAACT,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCS,OAAO,CAACT,KAAK,CAAC,SAAS,GAAAc,eAAA,GAAEd,KAAK,CAACW,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBO,MAAM,CAAC;MAChDZ,OAAO,CAACT,KAAK,CAAC,OAAO,GAAAe,gBAAA,GAAEf,KAAK,CAACW,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBF,IAAI,CAAC;MAE5C,IAAIS,YAAY,GAAG,8CAA8C;MAEjE,IAAI,EAAAN,gBAAA,GAAAhB,KAAK,CAACW,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,MAAK,GAAG,EAAE;QAClCC,YAAY,GAAG,0CAA0C;MAC3D,CAAC,MAAM,IAAI,EAAAL,gBAAA,GAAAjB,KAAK,CAACW,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBI,MAAM,MAAK,GAAG,EAAE;QACzCC,YAAY,GAAG,iDAAiD;MAClE,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAAlB,KAAK,CAACW,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;QACzCC,YAAY,GAAG,+CAA+C;MAChE,CAAC,MAAM,KAAAH,gBAAA,GAAInB,KAAK,CAACW,QAAQ,cAAAQ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,eAApBA,qBAAA,CAAsBG,OAAO,EAAE;QACxCD,YAAY,GAAGtB,KAAK,CAACW,QAAQ,CAACE,IAAI,CAACU,OAAO;MAC5C;MAEAtB,QAAQ,CAACqB,YAAY,CAAC;IACxB,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAIC,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MACzE,IAAI;QACF,MAAM1C,GAAG,CAAC2C,MAAM,CAAC,oBAAoBH,EAAE,EAAE,CAAC;QAC1C9B,YAAY,CAACD,SAAS,CAACmC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;QAClDM,KAAK,CAAC,gCAAgC,CAAC;MACzC,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACd+B,KAAK,CAAC,8CAA8C,CAAC;QACrDtB,OAAO,CAACT,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACtE;IACF;EACF,CAAC;EAED,MAAMgC,iBAAiB,GAAGtC,SAAS,CAACmC,MAAM,CAACI,QAAQ;IAAA,IAAAC,mBAAA;IAAA,OACjDD,QAAQ,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IAC/D,CAAC,EAAAF,mBAAA,GAAAD,QAAQ,CAACK,SAAS,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,GAAG,KAAI,EAAE,EAAEH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC;EAAA,CAClF,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGtC,WAAW,GAAGE,gBAAgB;EAC1D,MAAMqC,oBAAoB,GAAGD,mBAAmB,GAAGpC,gBAAgB;EACnE,MAAMsC,gBAAgB,GAAGV,iBAAiB,CAACW,KAAK,CAACF,oBAAoB,EAAED,mBAAmB,CAAC;EAC3F,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACd,iBAAiB,CAACe,MAAM,GAAG3C,gBAAgB,CAAC;EAEzE,MAAM4C,QAAQ,GAAIC,UAAU,IAAK9C,cAAc,CAAC8C,UAAU,CAAC;;EAE3D;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,UAAU,EAAEO,CAAC,EAAE,EAAE;IACpCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACrB;EAEA,IAAIvD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK8D,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC/D,OAAA;QAAK8D,SAAS,EAAC,6BAA6B;QAACE,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxD/D,OAAA;UAAM8D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI3D,KAAK,EAAE;IACT,oBACET,OAAA;MAAK8D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7B/D,OAAA;QAAK8D,SAAS,EAAC,oBAAoB;QAACE,IAAI,EAAC,OAAO;QAAAD,QAAA,EAC7CtD;MAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpE,OAAA;IAAK8D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B/D,OAAA;MAAK8D,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrE/D,OAAA;QAAA+D,QAAA,EAAI;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BpE,OAAA,CAACL,IAAI;QAAC0E,EAAE,EAAC,oBAAoB;QAACP,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAE1D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENpE,OAAA;MAAK8D,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClC/D,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/D,OAAA;UAAK8D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB/D,OAAA;YACEsE,IAAI,EAAC,MAAM;YACXR,SAAS,EAAC,cAAc;YACxBS,WAAW,EAAC,gDAA6C;YACzDC,KAAK,EAAEjE,UAAW;YAClBkE,QAAQ,EAAGC,CAAC,IAAK;cACflE,aAAa,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;cAC7B5D,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpE,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/D,OAAA;YAAO8D,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC9D/D,OAAA;cAAO8D,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eAC1C/D,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA;kBAAA+D,QAAA,EAAI;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACXpE,OAAA;kBAAA+D,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBpE,OAAA;kBAAA+D,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBpE,OAAA;kBAAA+D,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBpE,OAAA;kBAAA+D,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRpE,OAAA;cAAA+D,QAAA,EACGZ,gBAAgB,CAACK,MAAM,GAAG,CAAC,GAC1BL,gBAAgB,CAACyB,GAAG,CAAElC,QAAQ;gBAAA,IAAAmC,iBAAA,EAAAC,oBAAA;gBAAA,oBAC5B9E,OAAA;kBAAA+D,QAAA,gBACE/D,OAAA;oBAAI8D,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAErB,QAAQ,CAACR;kBAAE;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CpE,OAAA;oBAAA+D,QAAA,EAAKrB,QAAQ,CAACE;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBpE,OAAA;oBAAA+D,QAAA,EAAK,EAAAc,iBAAA,GAAAnC,QAAQ,CAACqC,OAAO,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBjC,KAAK,kBAAI5C,OAAA;sBAAM8D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrFpE,OAAA;oBAAA+D,QAAA,EAAK,EAAAe,oBAAA,GAAApC,QAAQ,CAACK,SAAS,cAAA+B,oBAAA,uBAAlBA,oBAAA,CAAoB9B,GAAG,kBAAIhD,OAAA;sBAAM8D,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChFpE,OAAA;oBAAI8D,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACzB/D,OAAA,CAACL,IAAI;sBACH0E,EAAE,EAAE,sBAAsB3B,QAAQ,CAACR,EAAE,EAAG;sBACxC4B,SAAS,EAAC,kCAAkC;sBAC5CkB,KAAK,EAAC,qBAAkB;sBAAAjB,QAAA,eAExB/D,OAAA,CAACF,GAAG;wBAAAmE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACPpE,OAAA,CAACL,IAAI;sBACH0E,EAAE,EAAE,uBAAuB3B,QAAQ,CAACR,EAAE,EAAG;sBACzC4B,SAAS,EAAC,qCAAqC;sBAC/CkB,KAAK,EAAC,UAAU;sBAAAjB,QAAA,eAEhB/D,OAAA,CAACJ,YAAY;wBAAAqE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACPpE,OAAA;sBACE8D,SAAS,EAAC,oCAAoC;sBAC9CmB,OAAO,EAAEA,CAAA,KAAMhD,YAAY,CAACS,QAAQ,CAACR,EAAE,CAAE;sBACzC8C,KAAK,EAAC,WAAW;sBAAAjB,QAAA,eAEjB/D,OAAA,CAACH,KAAK;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA3BE1B,QAAQ,CAACR,EAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BhB,CAAC;cAAA,CACN,CAAC,gBAEFpE,OAAA;gBAAA+D,QAAA,eACE/D,OAAA;kBAAIkF,OAAO,EAAC,GAAG;kBAACpB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAC/CxD,UAAU,GAAG,uBAAuB,GAAG;gBAA4B;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL3B,iBAAiB,CAACe,MAAM,GAAG3C,gBAAgB,iBAC1Cb,OAAA;UAAK,cAAW,sBAAsB;UAAC8D,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACrD/D,OAAA;YAAI8D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC/C/D,OAAA;cAAI8D,SAAS,EAAE,aAAanD,WAAW,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cAAAoD,QAAA,eAChE/D,OAAA;gBACE8D,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC9C,WAAW,GAAG,CAAC,CAAE;gBACzCwE,QAAQ,EAAExE,WAAW,KAAK,CAAE;gBAAAoD,QAAA,EAC7B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAEJT,WAAW,CAACiB,GAAG,CAACQ,MAAM,iBACrBpF,OAAA;cAAiB8D,SAAS,EAAE,aAAanD,WAAW,KAAKyE,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAArB,QAAA,eAChF/D,OAAA;gBACE8D,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC2B,MAAM,CAAE;gBAAArB,QAAA,EAE/BqB;cAAM;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GANFgB,MAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOX,CACL,CAAC,eAEFpE,OAAA;cAAI8D,SAAS,EAAE,aAAanD,WAAW,KAAK0C,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cAAAU,QAAA,eACzE/D,OAAA;gBACE8D,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC9C,WAAW,GAAG,CAAC,CAAE;gBACzCwE,QAAQ,EAAExE,WAAW,KAAK0C,UAAW;gBAAAU,QAAA,EACtC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLpE,OAAA;YAAK8D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B/D,OAAA;cAAO8D,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eACf,EAACb,oBAAoB,GAAG,CAAC,EAAC,QAAG,EAACI,IAAI,CAAC+B,GAAG,CAACpC,mBAAmB,EAAER,iBAAiB,CAACe,MAAM,CAAC,EAAC,OAAK,EAACf,iBAAiB,CAACe,MAAM,EAAC,YACpI;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CAlOID,mBAAmB;AAAAqF,EAAA,GAAnBrF,mBAAmB;AAoOzB,eAAeA,mBAAmB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}