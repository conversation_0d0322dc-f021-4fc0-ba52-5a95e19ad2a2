# Guide des Routes OphthalmoAI

## 🚀 Démarrage Rapide

### ✅ Installation et Configuration TERMINÉE

1. **Routes mises à jour** :
   - ✅ Ancien `routes/api.php` supprimé
   - ✅ Nouveau `routes/api.php` créé avec structure organisée
   - ✅ `routes/web.php` mis à jour
   - ✅ Fichiers temporaires nettoyés

2. **Vérifier la base de données** :
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

3. **Démarrer le serveur** :
   ```bash
   php artisan serve
   ```

## 📋 Routes Essentielles pour Démarrage

### 🔐 AUTHENTIFICATION (Priorité 1)

#### Médecins
```http
POST /api/auth/medecin/register
POST /api/auth/medecin/login
```

#### Patients  
```http
POST /api/auth/patient/register
POST /api/auth/patient/login
```

#### Admins
```http
POST /api/auth/admin/login
```

### 🩺 FONCTIONNALITÉS CORE (Priorité 1)

#### Questions
```http
GET    /api/questions              # Lister toutes les questions
POST   /api/questions              # Créer une question
GET    /api/questions/search       # Rechercher des questions
GET    /api/questions/{id}         # Détails d'une question
```

#### Réponses
```http
GET    /api/reponses               # Lister les réponses
POST   /api/reponses               # Créer une réponse
```

#### Catégories
```http
GET    /api/categories             # Lister les catégories
GET    /api/categories/{id}/questions-frequentes
```

### 💬 COMMUNICATION (Priorité 2)

#### Contact
```http
POST   /api/contact                # Envoyer un message
GET    /api/contact/messages       # Lister les messages
```

#### Commentaires
```http
GET    /api/comments               # Lister les commentaires
POST   /api/comments               # Ajouter un commentaire
```

### 🔒 ROUTES PROTÉGÉES (Priorité 2)

#### Profil Utilisateur
```http
GET    /api/profile                # Profil de l'utilisateur connecté
```

#### Médecins (avec auth:sanctum)
```http
GET    /api/medecin/profile        # Profil médecin
GET    /api/medecin/stats          # Statistiques médecin
GET    /api/medecin/questions      # Questions à traiter
POST   /api/medecin/reponses       # Créer une réponse
GET    /api/medecin/{id}/notifications  # Notifications
```

#### Patients (avec auth:sanctum)
```http
GET    /api/patient/profile        # Profil patient
GET    /api/patient/questions      # Questions du patient
POST   /api/patient/questions      # Poser une question
POST   /api/patient/rendez-vous    # Demander un RDV
```

## 🧪 Routes de Test (Développement)

### Vérification Système
```http
GET    /api/test/db                # Test connexion DB
GET    /api/test/api               # Test API
```

### Test d'Authentification
```http
GET    /api/profile                # Test token (avec Authorization: Bearer {token})
```

## 📱 Routes Web (Interface Utilisateur)

### Pages Publiques
```http
GET    /                           # Page d'accueil
GET    /about                      # À propos
GET    /contact                    # Contact
GET    /questions                  # Interface questions
```

### Authentification Web
```http
GET    /auth/login                 # Page de connexion
GET    /auth/register              # Page d'inscription
GET    /auth/medecin/login         # Connexion médecin
GET    /auth/patient/login         # Connexion patient
```

### Dashboards (Protégés)
```http
GET    /dashboard                  # Dashboard général
GET    /medecin/dashboard          # Dashboard médecin
GET    /patient/dashboard          # Dashboard patient
GET    /admin/dashboard            # Dashboard admin
```

## 🔧 Configuration Requise

### Headers API
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}  # Pour routes protégées
```

### Middleware
- `auth:sanctum` : Routes protégées
- `throttle:api` : Limitation de taux
- `cors` : Support CORS

## 📊 Ordre de Priorité pour Tests

### Phase 1 - Core (Immédiat)
1. ✅ Test connexion DB : `GET /api/test/db`
2. ✅ Inscription médecin : `POST /api/auth/medecin/register`
3. ✅ Connexion médecin : `POST /api/auth/medecin/login`
4. ✅ Créer question : `POST /api/questions`
5. ✅ Lister questions : `GET /api/questions`

### Phase 2 - Fonctionnalités (Semaine 1)
1. ✅ Créer réponse : `POST /api/reponses`
2. ✅ Profil utilisateur : `GET /api/profile`
3. ✅ Contact : `POST /api/contact`
4. ✅ Catégories : `GET /api/categories`
5. ✅ Commentaires : `POST /api/comments`

### Phase 3 - Avancé (Semaine 2)
1. ✅ Notifications : `GET /api/medecin/{id}/notifications`
2. ✅ Statistiques : `GET /api/medecin/stats`
3. ✅ Recherche : `GET /api/questions/search`
4. ✅ Admin panel : Routes `/api/admin/*`
5. ✅ Interface web : Routes `/dashboard`

## 🚨 Points d'Attention

### Sécurité
- Toutes les routes sensibles utilisent `auth:sanctum`
- Validation des données requise
- Tokens d'accès avec expiration

### Performance
- Pagination sur les listes
- Cache pour les catégories
- Index de recherche full-text

### Compatibilité
- Routes héritées maintenues
- Support API et Web
- Headers CORS configurés

## 📞 Support

Pour tester rapidement :
```bash
# Test de base
curl -X GET http://localhost:8000/api/test/api

# Test avec authentification
curl -X POST http://localhost:8000/api/auth/medecin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```
