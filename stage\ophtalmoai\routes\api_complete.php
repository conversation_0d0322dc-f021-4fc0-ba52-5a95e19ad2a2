<?php
/**
 * Routes API Complètes pour OphthalmoAI
 * Structure organisée pour démarrage rapide du projet
 * 
 * INSTRUCTIONS D'UTILISATION :
 * 1. Remplacez le contenu de routes/api.php par ce fichier
 * 2. Ou incluez ce fichier dans routes/api.php avec : require_once __DIR__ . '/api_complete.php';
 */

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\ReponseController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\MedecinController;
use App\Http\Controllers\PatientController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| ROUTES PUBLIQUES (Sans authentification)
|--------------------------------------------------------------------------
| Ces routes sont accessibles sans token d'authentification
*/

// ===== AUTHENTIFICATION =====
Route::prefix('auth')->group(function () {
    // Médecins
    Route::post('/medecin/register', [AuthController::class, 'register']);
    Route::post('/medecin/login', [AuthController::class, 'login']);
    
    // Patients (si vous avez un PatientController)
    Route::post('/patient/register', [PatientController::class, 'register']);
    Route::post('/patient/login', [PatientController::class, 'login']);
    
    // Admins
    Route::post('/admin/login', [AdminController::class, 'login']);
    
    // Logout général
    Route::post('/logout', function (Request $request) {
        $request->user()->currentAccessToken()->delete();
        return response()->json(['message' => 'Déconnecté avec succès']);
    })->middleware('auth:sanctum');
});

// ===== QUESTIONS PUBLIQUES =====
Route::prefix('questions')->group(function () {
    Route::get('/', [QuestionController::class, 'index']);
    Route::post('/', [QuestionController::class, 'store']);
    Route::get('/search', [QuestionController::class, 'search']);
    Route::get('/{id}', [QuestionController::class, 'show']);
    Route::get('/getReponse', [QuestionController::class, 'getReponse']);
});

// ===== CATÉGORIES =====
Route::prefix('categories')->group(function () {
    Route::get('/', [CategoryController::class, 'index']);
    Route::get('/{id}/questions-frequentes', [CategoryController::class, 'questionsFrequentes']);
});

// ===== RÉPONSES PUBLIQUES =====
Route::get('/reponses', [ReponseController::class, 'index']);

// ===== CONTACT =====
Route::prefix('contact')->group(function () {
    Route::post('/', [ContactController::class, 'store']);
    Route::get('/messages', [ContactController::class, 'index']);
});

// ===== COMMENTAIRES =====
Route::prefix('comments')->group(function () {
    Route::get('/', [CommentController::class, 'index']);
    Route::post('/', [CommentController::class, 'store']);
});

// ===== ROUTES DE TEST =====
Route::prefix('test')->group(function () {
    Route::get('/db', function () {
        try {
            \DB::connection()->getPdo();
            return response()->json(['status' => 'success', 'message' => 'Base de données connectée']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    });
    
    Route::get('/api', function () {
        return response()->json(['status' => 'success', 'message' => 'API OphthalmoAI fonctionnelle']);
    });
});

/*
|--------------------------------------------------------------------------
| ROUTES PROTÉGÉES (Avec authentification)
|--------------------------------------------------------------------------
| Ces routes nécessitent un token d'authentification valide
*/

Route::middleware('auth:sanctum')->group(function () {
    
    // ===== PROFIL UTILISATEUR =====
    Route::get('/profile', function (Request $request) {
        return response()->json([
            'user' => $request->user(),
            'type' => class_basename($request->user())
        ]);
    });
    
    // ===== MÉDECINS =====
    Route::prefix('medecin')->group(function () {
        Route::get('/profile', function (Request $request) {
            return $request->user();
        });
        Route::get('/stats', [MedecinController::class, 'getStats']);
        Route::get('/{medecinId}/messages', [ContactController::class, 'getMessagesForMedecin']);
        
        // Gestion des questions
        Route::get('/questions', [QuestionController::class, 'getQuestionsGestion']);
        Route::post('/questions/admin', [QuestionController::class, 'storeFromAdmin']);
        Route::put('/questions/{id}', [QuestionController::class, 'update']);
        Route::delete('/questions/{id}', [QuestionController::class, 'destroy']);
        
        // Gestion des réponses
        Route::post('/reponses', [ReponseController::class, 'store']);
        
        // Notifications
        Route::get('/{medecinId}/notifications', [NotificationController::class, 'index']);
        Route::patch('/notifications/{id}/mark-read', [NotificationController::class, 'markAsRead']);
        Route::patch('/{medecinId}/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    });
    
    // ===== PATIENTS =====
    Route::prefix('patient')->group(function () {
        Route::get('/profile', function (Request $request) {
            return $request->user();
        });
        Route::get('/questions', [QuestionController::class, 'getPatientQuestions']);
        Route::post('/questions', [QuestionController::class, 'store']);
        Route::post('/rendez-vous', [QuestionController::class, 'demandeConsultation']);
    });
    
    // ===== ADMIN =====
    Route::prefix('admin')->group(function () {
        Route::get('/notifications', [AdminController::class, 'getNotifications']);
        Route::get('/contacts', [ContactController::class, 'index']);
        
        // Gestion des utilisateurs
        Route::get('/medecins', [MedecinController::class, 'index']);
        Route::get('/patients', [PatientController::class, 'index']);
        
        // Gestion du contenu
        Route::post('/categories', [CategoryController::class, 'store']);
        Route::put('/categories/{id}', [CategoryController::class, 'update']);
        Route::delete('/categories/{id}', [CategoryController::class, 'destroy']);
    });
    
    // ===== NOTIFICATIONS GÉNÉRALES =====
    Route::get('/notifications', function (Request $request) {
        return $request->user()->notifications()->latest()->get();
    });
});

/*
|--------------------------------------------------------------------------
| ROUTES HÉRITÉES (Compatibilité avec l'existant)
|--------------------------------------------------------------------------
| Ces routes maintiennent la compatibilité avec votre code existant
*/

// Routes existantes maintenues pour compatibilité
Route::get('/questions-gestion', [QuestionController::class, 'getQuestionsGestion']);
Route::post('/reponses-gestion', [ReponseController::class, 'store']);
Route::get('/contacts', [ContactController::class, 'index']);
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Routes avec middleware existantes
Route::middleware('auth:sanctum')->get('/medecin-profile', function (Request $request) {
    return $request->user();
});

Route::get('/stats-medecin', [MedecinController::class, 'getStats'])
    ->middleware('auth:sanctum');

Route::get('/medecins/{medecinId}/notifications', [NotificationController::class, 'index']);
Route::patch('/notifications/{id}/mark-read', [NotificationController::class, 'markAsRead']);
Route::patch('/medecins/{medecinId}/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
