import React, { useState } from 'react';
import '../styles/Categories.css';

const CategoriesPage = () => {
  const [selectedCategory, setSelectedCategory] = useState(null);

  const categories = [
    { 
      id: 1, 
      nom: 'Cataracte', 
      image: '/cataracte.jpeg', 
      description: 'Lentille opacifiée, perte progressive de la vision.',
      details: 'La cataracte est une opacification du cristallin, ce qui entraîne une vision floue ou trouble. Les symptômes peuvent inclure une perte progressive de la vision, une gêne en cas de lumière vive et une vision nocturne altérée.'
    },
    { 
      id: 2, 
      nom: 'Glaucome', 
      image: '/glaucoma.jpg', 
      description: 'Problèmes de pression oculaire, pouvant endommager le nerf optique.',
      details: 'Le glaucome est une maladie oculaire qui résulte généralement d\'une pression intraoculaire élevée, endommageant le nerf optique. Il peut entraîner une perte de vision irréversible si non traité. Le traitement peut inclure des médicaments pour réduire la pression oculaire ou une chirurgie dans les cas graves.'
    },
    { 
      id: 3, 
      nom: 'Rétine', 
      image: '/retine.jpeg', 
      description: 'Troubles visuels liés à des problèmes rétiniens.',
      details: 'Les problèmes rétiniens peuvent inclure des maladies comme la dégénérescence maculaire, le décollement de la rétine ou la rétinopathie diabétique. Ces troubles affectent la vision centrale et périphérique et nécessitent souvent un suivi médical rapproché. Le traitement varie en fonction du type de problème rétinien, allant de l\'observation à la chirurgie laser.'
    },
  ];

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    // Ajoutez ici la logique de navigation ou d'affichage des détails
  };

  return (
    <section className="categories-section">
      <div className="container">
        <h1 className="categories-title">
          Nos Catégories Médicales
        </h1>
        
        <div className="row g-4">
          {categories.map((category) => (
            <div key={category.id} className="col-lg-4 col-md-6">
              <div 
                className="category-card"
                onClick={() => handleCategoryClick(category)}
              >
                <div className="category-img-container">
                  <img 
                    src={category.image} 
                    alt={category.nom}
                    className="category-img"
                  />
                  <div className="category-overlay">
                    <p className="category-overlay-text">
                      {category.details}
                    </p>
                    <button className="category-btn">
                      En savoir plus
                    </button>
                  </div>
                </div>
                
                <div className="category-content">
                  <h3 className="category-name">{category.nom}</h3>
                  <p className="category-description">
                    {category.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategoriesPage;
