// import React, { useState } from 'react';

// const AddResponseForm = ({ question, onClose }) => {
//   const [response, setResponse] = useState('');

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     await fetch('http://localhost:8000/api/responses', {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify({
//         question_id: question.id,
//         response,
//       }),
//     });
//     onClose();
//     alert('Réponse envoyée avec succès !');
//   };

//   return (
//     <div className="mt-4 p-4 border rounded bg-[#def3ff]">
//       <h3 className="text-md font-semibold mb-2">Répondre à : {question.message}</h3>
//       <form onSubmit={handleSubmit}>
//         <textarea
//           className="w-full p-2 border rounded mb-2"
//           value={response}
//           onChange={(e) => setResponse(e.target.value)}
//           rows="3"
//         />
//         <div className="flex gap-2">
//           <button type="submit" className="bg-[#008bd5] text-white px-4 py-1 rounded hover:bg-[#006fac]">
//             Envoyer
//           </button>
//           <button onClick={onClose} className="bg-gray-300 px-4 py-1 rounded">Annuler</button>
//         </div>
//       </form>
//     </div>
//   );
// };

// export default AddResponseForm;
