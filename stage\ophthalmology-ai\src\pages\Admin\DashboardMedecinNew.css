/* Styles généraux du dashboard */
.dashboard-content {
  padding: 15px;
  max-width: 1600px;
  margin: 0 auto;
  overflow-x: hidden;
}

/* En-tête de bienvenue */
.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.welcome-header h1 {
  color: #2d3748;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.date-display {
  color: #718096;
  margin-top: 4px;
  font-size: 0.85rem;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f7ff;
  color: #006fac;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-button:hover {
  background: #e0f2fe;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 111, 172, 0.1);
}

/* Statistiques rapides */
.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.stat-card.blue .stat-icon { background: #e0f2fe; color: #006fac; }
.stat-card.orange .stat-icon { background: #fff3e0; color: #ed8936; }
.stat-card.green .stat-icon { background: #f0fff4; color: #48bb78; }
.stat-card.purple .stat-icon { background: #faf5ff; color: #805ad5; }

.stat-info h3 {
  font-size: 0.8rem;
  color: #718096;
  margin: 0;
}

.stat-value {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

/* Rangées de widgets */
.dashboard-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

/* Cartes du dashboard */
.dashboard-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex: 1;
  min-width: 300px;
}

.dashboard-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-card.lg {
  flex: 2;
  min-width: 500px;
}

.dashboard-card.md {
  flex: 1;
  min-width: 300px;
}

.dashboard-card.full {
  flex: 1 1 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  color: #2d3748;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-body {
  padding: 16px;
  height: 220px;
  overflow-y: auto;
}

.chart-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-body {
  height: 180px !important;
  padding: 10px !important;
}

.dashboard-card.full .chart-body {
  height: 150px !important;
}

/* Sélecteur de période */
.period-selector {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  color: #4a5568;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-selector:hover {
  border-color: #cbd5e0;
}

.period-selector:focus {
  outline: none;
  border-color: #006fac;
  box-shadow: 0 0 0 3px rgba(0, 111, 172, 0.1);
}

/* Bouton Voir tout */
.view-all-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  color: #006fac;
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 8px;
}

.view-all-button:hover {
  background: #f0f7ff;
}

/* Bouton Ajouter */
.add-task-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f0f7ff;
  color: #006fac;
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 8px;
}

.add-task-button:hover {
  background: #e0f2fe;
}

/* Liste des rendez-vous */
.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.appointment-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  background: #f8fafc;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.appointment-item:hover {
  background: #f0f7ff;
  transform: translateX(5px);
}

.appointment-item.urgent {
  border-left: 3px solid #ff4757;
}

.appointment-time {
  font-weight: 600;
  color: #2d3748;
  width: 60px;
}

.appointment-details {
  flex: 1;
}

.appointment-details h4 {
  margin: 0;
  font-size: 0.85rem;
  color: #2d3748;
}

.appointment-details p {
  margin: 0;
  font-size: 0.75rem;
  color: #718096;
}

.appointment-status {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.appointment-status.confirmé { color: #48bb78; }
.appointment-status.en-attente { color: #ed8936; }
.appointment-status.urgent { color: #ff4757; }

/* Liste des patients */
.patients-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  background: #f8fafc;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.patient-item:hover {
  background: #f0f7ff;
  transform: translateX(5px);
}

.patient-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0f2fe;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #006fac;
  margin-right: 10px;
  font-size: 0.9rem;
}

.patient-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.patient-details {
  flex: 1;
}

.patient-details h4 {
  margin: 0;
  font-size: 0.85rem;
  color: #2d3748;
}

.patient-details p {
  margin: 0;
  font-size: 0.75rem;
  color: #718096;
}

.patient-visit {
  text-align: right;
  min-width: 100px;
}

.patient-visit small {
  color: #718096;
  font-size: 0.75rem;
}

.patient-visit p {
  margin: 0;
  color: #2d3748;
  font-size: 0.85rem;
}

/* Liste des tâches */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  background: #f8fafc;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.task-item:hover {
  background: #f0f7ff;
}

.task-item.completed {
  opacity: 0.7;
}

.task-item.completed .task-content p {
  text-decoration: line-through;
  color: #a0aec0;
}

.task-checkbox {
  margin-right: 12px;
}

.task-checkbox input[type="checkbox"] {
  display: none;
}

.task-checkbox label {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #cbd5e0;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-checkbox input[type="checkbox"]:checked + label {
  background: #006fac;
  border-color: #006fac;
}

.task-checkbox input[type="checkbox"]:checked + label:after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
}

.task-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-content p {
  margin: 0;
  font-size: 0.85rem;
  color: #2d3748;
}

.task-priority {
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: capitalize;
}

.task-priority.haute {
  background: #fff3f3;
  color: #ff4757;
}

.task-priority.moyenne {
  background: #fff3e0;
  color: #ed8936;
}

.task-priority.basse {
  background: #f0fff4;
  color: #48bb78;
}

/* Activité récente */
.activity-timeline {
  position: relative;
  padding-left: 24px;
}

.activity-timeline:before {
  content: '';
  position: absolute;
  left: 5px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.activity-item {
  position: relative;
  padding-bottom: 24px;
}

.activity-dot {
  position: absolute;
  left: -24px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #006fac;
  border: 3px solid #e0f2fe;
}

.activity-dot.consultation { background: #48bb78; }
.activity-dot.question { background: #ed8936; }
.activity-dot.rendez-vous { background: #805ad5; }

.activity-content p {
  margin: 0;
  color: #2d3748;
  font-size: 0.95rem;
}

.activity-content small {
  color: #718096;
  font-size: 0.85rem;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-card.lg,
  .dashboard-card.md {
    min-width: 100%;
  }

  .dashboard-row {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .welcome-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .welcome-actions {
    width: 100%;
  }

  .action-button {
    flex: 1;
    justify-content: center;
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Erreur et chargement */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: center;
  color: #ff4757;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #ff3040;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f7ff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #e0f2fe;
  border-top: 5px solid #006fac;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
