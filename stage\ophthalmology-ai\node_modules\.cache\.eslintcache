[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\LoadingSpinner.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\NotFound.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\Navbar.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\CategoriesPage.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\api.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\redux\\questionSlice.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ResponseDisplay.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\Footer.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\AboutPage.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Services.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\redux\\store2.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\store\\store.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\QuestionsList.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\actions\\actions.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\SearchQuestions.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\reducers\\rootReducer.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\reducers\\questionsReducer.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\SearchBar2.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ResponseCard.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\ContactPage.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\actions\\questionsActions.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\MedecinLogin.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\DashboardMedecin.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\PrivateRoute.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\MedecinRegister.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\DoctorForm.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\QuestionsManagement.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AddQuestion.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\EditQuestion.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\NotificationsPanel.jsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\ProfilMedecin.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AdminNotifications.jsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\DashboardLayout.jsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AdminNotificationsPanel.jsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ConsultationRequest.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\NotificationBell.js": "38"}, {"size": 398, "mtime": 1741916310507, "results": "39", "hashOfConfig": "40"}, {"size": 2632, "mtime": 1747950172150, "results": "41", "hashOfConfig": "40"}, {"size": 231, "mtime": 1740568997117, "results": "42", "hashOfConfig": "40"}, {"size": 110, "mtime": 1740733997100, "results": "43", "hashOfConfig": "40"}, {"size": 4180, "mtime": 1747950866722, "results": "44", "hashOfConfig": "40"}, {"size": 3385, "mtime": 1745285005433, "results": "45", "hashOfConfig": "40"}, {"size": 761, "mtime": 1748715944641, "results": "46", "hashOfConfig": "40"}, {"size": 2390, "mtime": 1741916452653, "results": "47", "hashOfConfig": "40"}, {"size": 3405, "mtime": 1741909023985, "results": "48", "hashOfConfig": "40"}, {"size": 2187, "mtime": 1745284539392, "results": "49", "hashOfConfig": "40"}, {"size": 5633, "mtime": 1745102442032, "results": "50", "hashOfConfig": "40"}, {"size": 1268, "mtime": 1741803963932, "results": "51", "hashOfConfig": "40"}, {"size": 278, "mtime": 1745013817722, "results": "52", "hashOfConfig": "40"}, {"size": 244, "mtime": 1745277535409, "results": "53", "hashOfConfig": "40"}, {"size": 1569, "mtime": 1741913690809, "results": "54", "hashOfConfig": "40"}, {"size": 332, "mtime": 1741916273433, "results": "55", "hashOfConfig": "40"}, {"size": 820, "mtime": 1741912879723, "results": "56", "hashOfConfig": "40"}, {"size": 230, "mtime": 1741963366936, "results": "57", "hashOfConfig": "40"}, {"size": 1735, "mtime": 1747947444647, "results": "58", "hashOfConfig": "40"}, {"size": 13064, "mtime": 1747948077465, "results": "59", "hashOfConfig": "40"}, {"size": 138, "mtime": 1741965272158, "results": "60", "hashOfConfig": "40"}, {"size": 4502, "mtime": 1745285208451, "results": "61", "hashOfConfig": "40"}, {"size": 2242, "mtime": 1747947336948, "results": "62", "hashOfConfig": "40"}, {"size": 3080, "mtime": 1747950457128, "results": "63", "hashOfConfig": "40"}, {"size": 22222, "mtime": 1747957415592, "results": "64", "hashOfConfig": "40"}, {"size": 367, "mtime": 1747950498901, "results": "65", "hashOfConfig": "40"}, {"size": 6097, "mtime": 1745421216034, "results": "66", "hashOfConfig": "40"}, {"size": 3074, "mtime": 1745276379531, "results": "67", "hashOfConfig": "40"}, {"size": 8704, "mtime": 1748716026384, "results": "68", "hashOfConfig": "40"}, {"size": 4852, "mtime": 1748715719152, "results": "69", "hashOfConfig": "40"}, {"size": 3135, "mtime": 1748715767303, "results": "70", "hashOfConfig": "40"}, {"size": 24116, "mtime": 1747953432205, "results": "71", "hashOfConfig": "40"}, {"size": 8489, "mtime": 1745421029910, "results": "72", "hashOfConfig": "40"}, {"size": 807, "mtime": 1745226765976, "results": "73", "hashOfConfig": "40"}, {"size": 3625, "mtime": 1747951498588, "results": "74", "hashOfConfig": "40"}, {"size": 17694, "mtime": 1747949012988, "results": "75", "hashOfConfig": "40"}, {"size": 8991, "mtime": 1747948804597, "results": "76", "hashOfConfig": "40"}, {"size": 9220, "mtime": 1747951469557, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lzweeu", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\App.js", ["192"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\Navbar.js", ["193"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\CategoriesPage.js", ["194"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\redux\\questionSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ResponseDisplay.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Services.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\redux\\store2.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\QuestionsList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\actions\\actions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\SearchQuestions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\reducers\\rootReducer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\reducers\\questionsReducer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\SearchBar2.js", ["195"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ResponseCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\ContactPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\actions\\questionsActions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\MedecinLogin.jsx", ["196", "197", "198"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\DashboardMedecin.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\PrivateRoute.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\MedecinRegister.jsx", ["199"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\DoctorForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\QuestionsManagement.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AddQuestion.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\EditQuestion.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\NotificationsPanel.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\ProfilMedecin.js", ["200", "201", "202"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AdminNotifications.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\DashboardLayout.jsx", ["203"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\pages\\Admin\\AdminNotificationsPanel.jsx", ["204"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\ConsultationRequest.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\stage\\ophthalmology-ai\\src\\Components\\NotificationBell.js", ["205", "206"], [], {"ruleId": "207", "severity": 1, "message": "208", "line": 1, "column": 25, "nodeType": "209", "messageId": "210", "endLine": 1, "endColumn": 29}, {"ruleId": "207", "severity": 1, "message": "211", "line": 14, "column": 11, "nodeType": "209", "messageId": "210", "endLine": 14, "endColumn": 20}, {"ruleId": "207", "severity": 1, "message": "212", "line": 5, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 5, "endColumn": 26}, {"ruleId": "213", "severity": 1, "message": "214", "line": 67, "column": 6, "nodeType": "215", "endLine": 67, "endColumn": 16, "suggestions": "216"}, {"ruleId": "207", "severity": 1, "message": "217", "line": 3, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 3, "endColumn": 21}, {"ruleId": "207", "severity": 1, "message": "218", "line": 3, "column": 23, "nodeType": "209", "messageId": "210", "endLine": 3, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "219", "line": 3, "column": 29, "nodeType": "209", "messageId": "210", "endLine": 3, "endColumn": 34}, {"ruleId": "207", "severity": 1, "message": "220", "line": 38, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 38, "endColumn": 21}, {"ruleId": "207", "severity": 1, "message": "221", "line": 4, "column": 32, "nodeType": "209", "messageId": "210", "endLine": 4, "endColumn": 40}, {"ruleId": "207", "severity": 1, "message": "222", "line": 16, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 16, "endColumn": 22}, {"ruleId": "207", "severity": 1, "message": "223", "line": 45, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 45, "endColumn": 26}, {"ruleId": "207", "severity": 1, "message": "224", "line": 3, "column": 41, "nodeType": "209", "messageId": "210", "endLine": 3, "endColumn": 50}, {"ruleId": "207", "severity": 1, "message": "225", "line": 16, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 16, "endColumn": 20}, {"ruleId": "207", "severity": 1, "message": "226", "line": 28, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 28, "endColumn": 15}, {"ruleId": "213", "severity": 1, "message": "227", "line": 169, "column": 6, "nodeType": "215", "endLine": 169, "endColumn": 15, "suggestions": "228"}, "no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", "'medecinId' is assigned a value but never used.", "'selectedCategory' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'randomQuestions'. Either include it or remove the dependency array.", "ArrayExpression", ["229"], "'UserCircle2' is defined but never used.", "'Lock' is defined but never used.", "'LogIn' is defined but never used.", "'response' is assigned a value but never used.", "'FiCamera' is defined but never used.", "'previewImage' is assigned a value but never used.", "'handlePhotoChange' is assigned a value but never used.", "'Calendar3' is defined but never used.", "'adminId' is assigned a value but never used.", "'id' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["230"], {"desc": "231", "fix": "232"}, {"desc": "233", "fix": "234"}, "Update the dependencies array to be: [dispatch, randomQuestions]", {"range": "235", "text": "236"}, "Update the dependencies array to be: [fetchNotifications, isAdmin]", {"range": "237", "text": "238"}, [2393, 2403], "[dispatch, randomQuestions]", [6234, 6243], "[fetchNotifications, isAdmin]"]