import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { FiEdit3, FiSave, FiX, FiCamera } from 'react-icons/fi';

const ProfilMedecin = () => {
  const [medecin, setMedecin] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    specialite: '',
    photo: null
  });
  const [previewImage, setPreviewImage] = useState(null);

  useEffect(() => {
    fetchMedecin();
  }, []);

  const fetchMedecin = async () => {
    try {
      const token = localStorage.getItem('medecinToken');
      const res = await axios.get('http://localhost:8000/api/medecin-profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setMedecin(res.data);
      setFormData({
        nom: res.data.nom || '',
        email: res.data.email || '',
        specialite: res.data.specialite || '',
        photo: null
      });
      setPreviewImage(res.data.photo);
    } catch (err) {
      toast.error("Erreur lors du chargement du profil");
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData({ ...formData, photo: file });
      setPreviewImage(URL.createObjectURL(file));
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('medecinToken');
      const formDataToSend = new FormData();
      
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null) {
          formDataToSend.append(key, formData[key]);
        }
      });

      const res = await axios.put(
        'http://localhost:8000/api/medecin-profile',
        formDataToSend,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      setMedecin(res.data);
      setIsEditing(false);
      toast.success("Profil mis à jour avec succès");
    } catch (err) {
      toast.error("Erreur lors de la mise à jour du profil");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white shadow-xl rounded-lg overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 px-8 py-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Mon Profil</h2>
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center px-4 py-2 bg-white text-blue-600 rounded-md hover:bg-blue-50 transition-colors duration-200"
                >
                  <FiEdit3 className="mr-2" />
                  Modifier
                </button>
              )}
            </div>
          </div>

          {/* Photo de profil */}
          {/* <div className="relative -mt-20 px-8">
            <div className="relative inline-block">
              <img
                src={previewImage || '/default-avatar.png'}
                alt="Photo de profil"
                className="w-40 h-40 rounded-full border-4 border-white shadow-lg object-cover"
              /> */}
              {/* {isEditing && (
                <label className="absolute bottom-2 right-2 bg-blue-600 rounded-full p-3 cursor-pointer hover:bg-blue-700 transition-colors duration-200">
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handlePhotoChange}
                  />
                  <FiCamera className="text-white w-5 h-5" />
                </label>
              )} */}
            {/* </div>
          </div> */}

          {/* Contenu du profil */}
          <div className="px-8 py-6">
            {isEditing ? (
              <form onSubmit={handleSave} className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom complet
                    </label>
                    <input
                      type="text"
                      name="nom"
                      value={formData.nom}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Spécialité
                    </label>
                    <input
                      type="text"
                      name="specialite"
                      value={formData.specialite}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setIsEditing(false);
                      setPreviewImage(medecin.photo);
                    }}
                    className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                  >
                    <FiX className="mr-2" />
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                  >
                    <FiSave className="mr-2" />
                    Enregistrer
                  </button>
                </div>
              </form>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Nom complet</h3>
                    <p className="mt-1 text-lg text-gray-900">{medecin.nom}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Email</h3>
                    <p className="mt-1 text-lg text-gray-900">{medecin.email}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Spécialité</h3>
                    <p className="mt-1 text-lg text-gray-900">{medecin.specialite}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilMedecin;
