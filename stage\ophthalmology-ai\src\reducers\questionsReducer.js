// reducers/questionsReducer.js

// État initial
const initialState = {
  questions: [],            // Toutes les questions
  filteredQuestions: [],    // Questions filtrées après recherche
  loading: false,           // Pour indiquer le chargement
  error: null               // Pour gérer les erreurs éventuelles
};

// Reducer
const questionsReducer = (state = initialState, action) => {
  switch (action.type) {

    // Remplir toutes les questions depuis l’API
    case 'SET_QUESTIONS':
      return {
        ...state,
        questions: action.payload,
        filteredQuestions: action.payload, // pour afficher toutes les questions par défaut
        loading: false,
        error: null
      };

    // Filtrer les questions selon un mot-clé
    case 'SET_FILTERED_QUESTIONS':
      return {
        ...state,
        filteredQuestions: action.payload,
        error: null,
        loading: false
      };

    // Indiquer que les données sont en cours de chargement
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
        // Si on commence à charger, on efface l'erreur précédente
        ...(action.payload ? { error: null } : {})
      };

    // En cas d'erreur lors de la requête API
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    // Nettoyer les résultats de recherche
    case 'CLEAR_RESPONSE':
      return {
        ...state,
        filteredQuestions: [],
        error: null
      };

    // Cas par défaut : on retourne l’état inchangé
    default:
      return state;
  }
};

export default questionsReducer;
