{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\stage\\\\ophthalmology-ai\\\\src\\\\pages\\\\Admin\\\\QuestionsManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport api from \"../../api\";\nimport { Link } from \"react-router-dom\";\nimport { PencilSquare, Trash, Eye } from 'react-bootstrap-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuestionsManagement = () => {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [error, setError] = useState(null);\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(10);\n  useEffect(() => {\n    fetchQuestions();\n  }, []);\n  const fetchQuestions = async () => {\n    try {\n      setLoading(true);\n      const response = await api.get('/admin/questions');\n      console.log('Questions reçues:', response.data); // Pour déboguer\n      setQuestions(response.data);\n      setError(null);\n    } catch (error) {\n      setError('Erreur lors de la récupération des questions');\n      console.error('Erreur lors de la récupération des questions', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {\n      try {\n        await api.delete(`/questions/${id}`);\n        setQuestions(questions.filter(q => q.id !== id));\n        alert(\"Question supprimée avec succès\");\n      } catch (error) {\n        alert(\"Erreur lors de la suppression de la question\");\n        console.error(\"Erreur lors de la suppression de la question\", error);\n      }\n    }\n  };\n  const filteredQuestions = questions.filter(question => {\n    var _question$categorie;\n    return question.texte.toLowerCase().includes(searchTerm.toLowerCase()) || (((_question$categorie = question.categorie) === null || _question$categorie === void 0 ? void 0 : _question$categorie.nom) || '').toLowerCase().includes(searchTerm.toLowerCase());\n  });\n\n  // Pagination logic\n  const indexOfLastQuestion = currentPage * questionsPerPage;\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\n  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);\n  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Generate page numbers\n  const pageNumbers = [];\n  for (let i = 1; i <= totalPages; i++) {\n    pageNumbers.push(i);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-4 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Chargement...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mt-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-danger\",\n        role: \"alert\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Gestion des Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/questions/ajouter\",\n        className: \"btn btn-primary\",\n        children: \"+ Ajouter une question\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card shadow-sm mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Rechercher une question ou une cat\\xE9gorie...\",\n            value: searchTerm,\n            onChange: e => {\n              setSearchTerm(e.target.value);\n              setCurrentPage(1); // Reset to first page on search\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-responsive\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table table-hover table-bordered align-middle\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"table-primary text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Question\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"R\\xE9ponse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: currentQuestions.length > 0 ? currentQuestions.map(question => {\n                var _question$reponse, _question$categorie2;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-center\",\n                    children: question.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: question.texte\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_question$reponse = question.reponse) === null || _question$reponse === void 0 ? void 0 : _question$reponse.texte) || /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Non d\\xE9finie\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: ((_question$categorie2 = question.categorie) === null || _question$categorie2 === void 0 ? void 0 : _question$categorie2.nom) || /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-muted\",\n                      children: \"Aucune\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: `/questions/details/${question.id}`,\n                      className: \"btn btn-sm btn-outline-info mx-1\",\n                      title: \"Voir les d\\xE9tails\",\n                      children: /*#__PURE__*/_jsxDEV(Eye, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 136,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Link, {\n                      to: `/questions/modifier/${question.id}`,\n                      className: \"btn btn-sm btn-outline-warning mx-1\",\n                      title: \"Modifier\",\n                      children: /*#__PURE__*/_jsxDEV(PencilSquare, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-danger mx-1\",\n                      onClick: () => handleDelete(question.id),\n                      title: \"Supprimer\",\n                      children: /*#__PURE__*/_jsxDEV(Trash, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 150,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 23\n                  }, this)]\n                }, question.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"5\",\n                  className: \"text-center text-muted\",\n                  children: searchTerm ? \"Aucun résultat trouvé\" : \"Aucune question disponible\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), filteredQuestions.length > questionsPerPage && /*#__PURE__*/_jsxDEV(\"nav\", {\n          \"aria-label\": \"Questions pagination\",\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"pagination justify-content-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === 1 ? 'disabled' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(currentPage - 1),\n                disabled: currentPage === 1,\n                children: \"Pr\\xE9c\\xE9dent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === number ? 'active' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(number),\n                children: number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 21\n              }, this)\n            }, number, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `page-item ${currentPage === totalPages ? 'disabled' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"page-link\",\n                onClick: () => paginate(currentPage + 1),\n                disabled: currentPage === totalPages,\n                children: \"Suivant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Affichage de \", indexOfFirstQuestion + 1, \" \\xE0 \", Math.min(indexOfLastQuestion, filteredQuestions.length), \" sur \", filteredQuestions.length, \" questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionsManagement, \"MhonT3Ej58OhvPsqS1VklmgNosI=\");\n_c = QuestionsManagement;\nexport default QuestionsManagement;\nvar _c;\n$RefreshReg$(_c, \"QuestionsManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "Link", "PencilSquare", "Trash", "Eye", "jsxDEV", "_jsxDEV", "QuestionsManagement", "_s", "questions", "setQuestions", "loading", "setLoading", "searchTerm", "setSearchTerm", "error", "setError", "currentPage", "setCurrentPage", "questionsPerPage", "fetchQuestions", "response", "get", "console", "log", "data", "handleDelete", "id", "window", "confirm", "delete", "filter", "q", "alert", "filteredQuestions", "question", "_question$categorie", "texte", "toLowerCase", "includes", "categorie", "nom", "indexOfLastQuestion", "indexOfFirstQuestion", "currentQuestions", "slice", "totalPages", "Math", "ceil", "length", "paginate", "pageNumber", "pageNumbers", "i", "push", "className", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "placeholder", "value", "onChange", "e", "target", "map", "_question$reponse", "_question$categorie2", "reponse", "title", "onClick", "colSpan", "disabled", "number", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/stage/ophthalmology-ai/src/pages/Admin/QuestionsManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport api from \"../../api\";\r\nimport { <PERSON> } from \"react-router-dom\";\r\nimport { PencilSquare, Trash, Eye } from 'react-bootstrap-icons';\r\n\r\nconst QuestionsManagement = () => {\r\n  const [questions, setQuestions] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Pagination states\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [questionsPerPage] = useState(10);\r\n\r\n  useEffect(() => {\r\n    fetchQuestions();\r\n  }, []);\r\n\r\n  const fetchQuestions = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await api.get('/admin/questions');\r\n      console.log('Questions reçues:', response.data); // Pour déboguer\r\n      setQuestions(response.data);\r\n      setError(null);\r\n    } catch (error) {\r\n      setError('Erreur lors de la récupération des questions');\r\n      console.error('Erreur lors de la récupération des questions', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette question ?')) {\r\n      try {\r\n        await api.delete(`/questions/${id}`);\r\n        setQuestions(questions.filter((q) => q.id !== id));\r\n        alert(\"Question supprimée avec succès\");\r\n      } catch (error) {\r\n        alert(\"Erreur lors de la suppression de la question\");\r\n        console.error(\"Erreur lors de la suppression de la question\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const filteredQuestions = questions.filter(question =>\r\n    question.texte.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (question.categorie?.nom || '').toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  // Pagination logic\r\n  const indexOfLastQuestion = currentPage * questionsPerPage;\r\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\r\n  const currentQuestions = filteredQuestions.slice(indexOfFirstQuestion, indexOfLastQuestion);\r\n  const totalPages = Math.ceil(filteredQuestions.length / questionsPerPage);\r\n\r\n  const paginate = (pageNumber) => setCurrentPage(pageNumber);\r\n\r\n  // Generate page numbers\r\n  const pageNumbers = [];\r\n  for (let i = 1; i <= totalPages; i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"container mt-4 text-center\">\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Chargement...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"container mt-4\">\r\n        <div className=\"alert alert-danger\" role=\"alert\">\r\n          {error}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mt-4\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n        <h2>Gestion des Questions</h2>\r\n        <Link to=\"/questions/ajouter\" className=\"btn btn-primary\">\r\n          + Ajouter une question\r\n        </Link>\r\n      </div>\r\n\r\n      <div className=\"card shadow-sm mb-4\">\r\n        <div className=\"card-body\">\r\n          <div className=\"mb-3\">\r\n            <input\r\n              type=\"text\"\r\n              className=\"form-control\"\r\n              placeholder=\"Rechercher une question ou une catégorie...\"\r\n              value={searchTerm}\r\n              onChange={(e) => {\r\n                setSearchTerm(e.target.value);\r\n                setCurrentPage(1); // Reset to first page on search\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"table-responsive\">\r\n            <table className=\"table table-hover table-bordered align-middle\">\r\n              <thead className=\"table-primary text-center\">\r\n                <tr>\r\n                  <th>ID</th>\r\n                  <th>Question</th>\r\n                  <th>Réponse</th>\r\n                  <th>Catégorie</th>\r\n                  <th>Actions</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {currentQuestions.length > 0 ? (\r\n                  currentQuestions.map((question) => (\r\n                    <tr key={question.id}>\r\n                      <td className=\"text-center\">{question.id}</td>\r\n                      <td>{question.texte}</td>\r\n                      <td>{question.reponse?.texte || <span className=\"text-muted\">Non définie</span>}</td>\r\n                      <td>{question.categorie?.nom || <span className=\"text-muted\">Aucune</span>}</td>\r\n                      <td className=\"text-center\">\r\n                        <Link\r\n                          to={`/questions/details/${question.id}`}\r\n                          className=\"btn btn-sm btn-outline-info mx-1\"\r\n                          title=\"Voir les détails\"\r\n                        >\r\n                          <Eye />\r\n                        </Link>\r\n                        <Link\r\n                          to={`/questions/modifier/${question.id}`}\r\n                          className=\"btn btn-sm btn-outline-warning mx-1\"\r\n                          title=\"Modifier\"\r\n                        >\r\n                          <PencilSquare />\r\n                        </Link>\r\n                        <button\r\n                          className=\"btn btn-sm btn-outline-danger mx-1\"\r\n                          onClick={() => handleDelete(question.id)}\r\n                          title=\"Supprimer\"\r\n                        >\r\n                          <Trash />\r\n                        </button>\r\n                      </td>\r\n                    </tr>\r\n                  ))\r\n                ) : (\r\n                  <tr>\r\n                    <td colSpan=\"5\" className=\"text-center text-muted\">\r\n                      {searchTerm ? \"Aucun résultat trouvé\" : \"Aucune question disponible\"}\r\n                    </td>\r\n                  </tr>\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          {/* Pagination */}\r\n          {filteredQuestions.length > questionsPerPage && (\r\n            <nav aria-label=\"Questions pagination\" className=\"mt-4\">\r\n              <ul className=\"pagination justify-content-center\">\r\n                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\r\n                  <button\r\n                    className=\"page-link\"\r\n                    onClick={() => paginate(currentPage - 1)}\r\n                    disabled={currentPage === 1}\r\n                  >\r\n                    Précédent\r\n                  </button>\r\n                </li>\r\n                \r\n                {pageNumbers.map(number => (\r\n                  <li key={number} className={`page-item ${currentPage === number ? 'active' : ''}`}>\r\n                    <button\r\n                      className=\"page-link\"\r\n                      onClick={() => paginate(number)}\r\n                    >\r\n                      {number}\r\n                    </button>\r\n                  </li>\r\n                ))}\r\n                \r\n                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\r\n                  <button\r\n                    className=\"page-link\"\r\n                    onClick={() => paginate(currentPage + 1)}\r\n                    disabled={currentPage === totalPages}\r\n                  >\r\n                    Suivant\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n              <div className=\"text-center mt-2\">\r\n                <small className=\"text-muted\">\r\n                  Affichage de {indexOfFirstQuestion + 1} à {Math.min(indexOfLastQuestion, filteredQuestions.length)} sur {filteredQuestions.length} questions\r\n                </small>\r\n              </div>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsManagement;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,YAAY,EAAEC,KAAK,EAAEC,GAAG,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqB,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAEvCC,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,QAAQ,GAAG,MAAMrB,GAAG,CAACsB,GAAG,CAAC,kBAAkB,CAAC;MAClDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;MACjDf,YAAY,CAACW,QAAQ,CAACI,IAAI,CAAC;MAC3BT,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdC,QAAQ,CAAC,8CAA8C,CAAC;MACxDO,OAAO,CAACR,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACtE,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAIC,MAAM,CAACC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MACzE,IAAI;QACF,MAAM7B,GAAG,CAAC8B,MAAM,CAAC,cAAcH,EAAE,EAAE,CAAC;QACpCjB,YAAY,CAACD,SAAS,CAACsB,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;QAClDM,KAAK,CAAC,gCAAgC,CAAC;MACzC,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdkB,KAAK,CAAC,8CAA8C,CAAC;QACrDV,OAAO,CAACR,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACtE;IACF;EACF,CAAC;EAED,MAAMmB,iBAAiB,GAAGzB,SAAS,CAACsB,MAAM,CAACI,QAAQ;IAAA,IAAAC,mBAAA;IAAA,OACjDD,QAAQ,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1B,UAAU,CAACyB,WAAW,CAAC,CAAC,CAAC,IAC/D,CAAC,EAAAF,mBAAA,GAAAD,QAAQ,CAACK,SAAS,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,GAAG,KAAI,EAAE,EAAEH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1B,UAAU,CAACyB,WAAW,CAAC,CAAC,CAAC;EAAA,CAClF,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGzB,WAAW,GAAGE,gBAAgB;EAC1D,MAAMwB,oBAAoB,GAAGD,mBAAmB,GAAGvB,gBAAgB;EACnE,MAAMyB,gBAAgB,GAAGV,iBAAiB,CAACW,KAAK,CAACF,oBAAoB,EAAED,mBAAmB,CAAC;EAC3F,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACd,iBAAiB,CAACe,MAAM,GAAG9B,gBAAgB,CAAC;EAEzE,MAAM+B,QAAQ,GAAIC,UAAU,IAAKjC,cAAc,CAACiC,UAAU,CAAC;;EAE3D;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,UAAU,EAAEO,CAAC,EAAE,EAAE;IACpCD,WAAW,CAACE,IAAI,CAACD,CAAC,CAAC;EACrB;EAEA,IAAI1C,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKiD,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzClD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAACE,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxDlD,OAAA;UAAMiD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,KAAK,EAAE;IACT,oBACET,OAAA;MAAKiD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BlD,OAAA;QAAKiD,SAAS,EAAC,oBAAoB;QAACE,IAAI,EAAC,OAAO;QAAAD,QAAA,EAC7CzC;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAKiD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BlD,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrElD,OAAA;QAAAkD,QAAA,EAAI;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BvD,OAAA,CAACL,IAAI;QAAC6D,EAAE,EAAC,oBAAoB;QAACP,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAE1D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENvD,OAAA;MAAKiD,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClClD,OAAA;QAAKiD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlD,OAAA;YACEyD,IAAI,EAAC,MAAM;YACXR,SAAS,EAAC,cAAc;YACxBS,WAAW,EAAC,gDAA6C;YACzDC,KAAK,EAAEpD,UAAW;YAClBqD,QAAQ,EAAGC,CAAC,IAAK;cACfrD,aAAa,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;cAC7B/C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvD,OAAA;UAAKiD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BlD,OAAA;YAAOiD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC9DlD,OAAA;cAAOiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eAC1ClD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAAkD,QAAA,EAAI;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACXvD,OAAA;kBAAAkD,QAAA,EAAI;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBvD,OAAA;kBAAAkD,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBvD,OAAA;kBAAAkD,QAAA,EAAI;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBvD,OAAA;kBAAAkD,QAAA,EAAI;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRvD,OAAA;cAAAkD,QAAA,EACGZ,gBAAgB,CAACK,MAAM,GAAG,CAAC,GAC1BL,gBAAgB,CAACyB,GAAG,CAAElC,QAAQ;gBAAA,IAAAmC,iBAAA,EAAAC,oBAAA;gBAAA,oBAC5BjE,OAAA;kBAAAkD,QAAA,gBACElD,OAAA;oBAAIiD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAErB,QAAQ,CAACR;kBAAE;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CvD,OAAA;oBAAAkD,QAAA,EAAKrB,QAAQ,CAACE;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBvD,OAAA;oBAAAkD,QAAA,EAAK,EAAAc,iBAAA,GAAAnC,QAAQ,CAACqC,OAAO,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBjC,KAAK,kBAAI/B,OAAA;sBAAMiD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrFvD,OAAA;oBAAAkD,QAAA,EAAK,EAAAe,oBAAA,GAAApC,QAAQ,CAACK,SAAS,cAAA+B,oBAAA,uBAAlBA,oBAAA,CAAoB9B,GAAG,kBAAInC,OAAA;sBAAMiD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChFvD,OAAA;oBAAIiD,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACzBlD,OAAA,CAACL,IAAI;sBACH6D,EAAE,EAAE,sBAAsB3B,QAAQ,CAACR,EAAE,EAAG;sBACxC4B,SAAS,EAAC,kCAAkC;sBAC5CkB,KAAK,EAAC,qBAAkB;sBAAAjB,QAAA,eAExBlD,OAAA,CAACF,GAAG;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACPvD,OAAA,CAACL,IAAI;sBACH6D,EAAE,EAAE,uBAAuB3B,QAAQ,CAACR,EAAE,EAAG;sBACzC4B,SAAS,EAAC,qCAAqC;sBAC/CkB,KAAK,EAAC,UAAU;sBAAAjB,QAAA,eAEhBlD,OAAA,CAACJ,YAAY;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACPvD,OAAA;sBACEiD,SAAS,EAAC,oCAAoC;sBAC9CmB,OAAO,EAAEA,CAAA,KAAMhD,YAAY,CAACS,QAAQ,CAACR,EAAE,CAAE;sBACzC8C,KAAK,EAAC,WAAW;sBAAAjB,QAAA,eAEjBlD,OAAA,CAACH,KAAK;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA3BE1B,QAAQ,CAACR,EAAE;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BhB,CAAC;cAAA,CACN,CAAC,gBAEFvD,OAAA;gBAAAkD,QAAA,eACElD,OAAA;kBAAIqE,OAAO,EAAC,GAAG;kBAACpB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAC/C3C,UAAU,GAAG,uBAAuB,GAAG;gBAA4B;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL3B,iBAAiB,CAACe,MAAM,GAAG9B,gBAAgB,iBAC1Cb,OAAA;UAAK,cAAW,sBAAsB;UAACiD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACrDlD,OAAA;YAAIiD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC/ClD,OAAA;cAAIiD,SAAS,EAAE,aAAatC,WAAW,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cAAAuC,QAAA,eAChElD,OAAA;gBACEiD,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAACjC,WAAW,GAAG,CAAC,CAAE;gBACzC2D,QAAQ,EAAE3D,WAAW,KAAK,CAAE;gBAAAuC,QAAA,EAC7B;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,EAEJT,WAAW,CAACiB,GAAG,CAACQ,MAAM,iBACrBvE,OAAA;cAAiBiD,SAAS,EAAE,aAAatC,WAAW,KAAK4D,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAArB,QAAA,eAChFlD,OAAA;gBACEiD,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC2B,MAAM,CAAE;gBAAArB,QAAA,EAE/BqB;cAAM;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GANFgB,MAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOX,CACL,CAAC,eAEFvD,OAAA;cAAIiD,SAAS,EAAE,aAAatC,WAAW,KAAK6B,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;cAAAU,QAAA,eACzElD,OAAA;gBACEiD,SAAS,EAAC,WAAW;gBACrBmB,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAACjC,WAAW,GAAG,CAAC,CAAE;gBACzC2D,QAAQ,EAAE3D,WAAW,KAAK6B,UAAW;gBAAAU,QAAA,EACtC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLvD,OAAA;YAAKiD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BlD,OAAA;cAAOiD,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eACf,EAACb,oBAAoB,GAAG,CAAC,EAAC,QAAG,EAACI,IAAI,CAAC+B,GAAG,CAACpC,mBAAmB,EAAER,iBAAiB,CAACe,MAAM,CAAC,EAAC,OAAK,EAACf,iBAAiB,CAACe,MAAM,EAAC,YACpI;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CA9MID,mBAAmB;AAAAwE,EAAA,GAAnBxE,mBAAmB;AAgNzB,eAAeA,mBAAmB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}