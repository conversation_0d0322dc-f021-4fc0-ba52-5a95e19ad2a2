<?php

namespace Database\Seeders;

use App\Models\Medecin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class MedecinAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un médecin super-admin
        $superAdmin = Medecin::create([
            'nom' => 'Dr. Admin Principal',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'specialite' => 'Ophtalmologie',
            'is_admin' => true,
            'admin_role' => 'super_admin',
            'admin_granted_at' => now(),
        ]);

        // Créer un médecin admin normal
        $admin = Medecin::create([
            'nom' => 'Dr. Admin Secondaire',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'specialite' => 'Chirurgie Ophtalmologique',
            'is_admin' => true,
            'admin_role' => 'admin',
            'admin_granted_at' => now(),
        ]);

        // Créer un médecin modérateur
        $moderator = Medecin::create([
            'nom' => 'Dr. Modérateur',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'specialite' => 'Ophtalmologie Pédiatrique',
            'is_admin' => true,
            'admin_role' => 'moderator',
            'admin_granted_at' => now(),
        ]);

        // Créer un médecin normal (sans privilèges admin)
        $medecin = Medecin::create([
            'nom' => 'Dr. Médecin Normal',
            'email' => '<EMAIL>',
            'password' => Hash::make('medecin123'),
            'specialite' => 'Ophtalmologie Générale',
            'is_admin' => false,
        ]);

        $this->command->info('Médecins créés avec succès :');
        $this->command->info('- Super Admin: <EMAIL> / admin123');
        $this->command->info('- Admin: <EMAIL> / admin123');
        $this->command->info('- Modérateur: <EMAIL> / admin123');
        $this->command->info('- Médecin: <EMAIL> / medecin123');
    }
}
