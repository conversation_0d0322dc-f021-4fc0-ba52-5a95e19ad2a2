// import React, { useEffect, useState } from 'react';
// import AddResponseForm from './AddResponseForm';

// const QuestionsList = () => {
//   const [questions, setQuestions] = useState([]);
//   const [selectedQuestion, setSelectedQuestion] = useState(null);

//   useEffect(() => {
//     fetch('http://localhost:8000/api/questions-gestion')
//       .then(res => res.json())
//       .then(data => setQuestions(data));
//   }, []);

//   return (
//     <div className="bg-white p-4 rounded-2xl shadow-md">
//       <h2 className="text-xl font-semibold text-[#005e8e] mb-4">❓ Questions posées</h2>
//       {questions.length > 0 ? (
//         questions.map((q) => (
//           <div key={q.id} className="border-b py-3">
//             <p className="text-gray-800">{q.message}</p>
//             <button
//               className="mt-2 text-sm text-blue-600 hover:underline"
//               onClick={() => setSelectedQuestion(q)}
//             >
//               ➕ Répondre
//             </button>
//           </div>
//         ))
//       ) : (
//         <p className="text-sm text-gray-500">Aucune question disponible.</p>
//       )}

//       {selectedQuestion && (
//         <AddResponseForm question={selectedQuestion} onClose={() => setSelectedQuestion(null)} />
//       )}
//     </div>
//   );
// };

// export default QuestionsList;
