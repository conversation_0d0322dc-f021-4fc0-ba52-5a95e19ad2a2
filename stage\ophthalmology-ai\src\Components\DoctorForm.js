import React, { useState } from "react";
import axios from "axios";

const DoctorForm = ({ specialite, medecinId }) => {
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    question: "",
  });
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await axios.post("http://localhost:8000/api/contact", {
        name: formData.name,
        phone: formData.phone,
        message: formData.question,
        specialite,
        medecin_id: medecinId,
      });

      setMessage("✅ Votre demande a été envoyée avec succès.");
      setFormData({ name: "", phone: "", question: "" });
    } catch (error) {
      console.error(error.response?.data || error.message);
      setMessage("❌ Une erreur est survenue. Veuillez réessayer.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="form-container">
      <div className="form-header">
        <h2>Demande de Consultation</h2>
        <p>Remplissez le formulaire ci-dessous pour prendre rendez-vous</p>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Nom complet *</label>
            <input
              type="text"
              name="name"
              className="form-input"
              value={formData.name}
              onChange={handleChange}
              placeholder="Entrez votre nom"
              required
            />
          </div>

          <div className="form-group">
            <label className="form-label">Téléphone</label>
            <input
              type="tel"
              name="phone"
              className="form-input"
              value={formData.phone}
              onChange={handleChange}
              placeholder="06 00 00 00 00"
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Votre question *</label>
          <textarea
            name="question"
            className="form-input form-textarea"
            value={formData.question}
            onChange={handleChange}
            placeholder="Décrivez votre problème ou question"
            required
          />
        </div>

        <button
          type="submit"
          className="form-button form-button-primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Envoi en cours..." : "Envoyer la demande"}
        </button>

        {message && (
          <div className={message.includes("✅") ? "form-success" : "form-error"}>
            {message}
          </div>
        )}
      </form>
    </div>
  );
};

export default DoctorForm;
