<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('medecins', function (Blueprint $table) {
            $table->boolean('is_admin')->default(false)->after('specialite');
            $table->string('admin_role')->nullable()->after('is_admin'); // 'super_admin', 'admin', 'moderator'
            $table->timestamp('admin_granted_at')->nullable()->after('admin_role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medecins', function (Blueprint $table) {
            $table->dropColumn(['is_admin', 'admin_role', 'admin_granted_at']);
        });
    }
};
