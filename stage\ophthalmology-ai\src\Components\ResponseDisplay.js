import { useSelector, useDispatch } from "react-redux";
import { useState } from "react";
import { startLoading, setResponse, setError } from "../redux/questionSlice";

// Fonction pour simuler la récupération de la réponse
const fetchResponse = async (query) => {
  console.log("Envoi de la requête avec : ", query);  // Log de la requête
  try {
    const response = await fetch(`/api/questions/search?query=${query}`);
    const data = await response.json();
    console.log("Réponse reçue de l'API : ", data);  // Log de la réponse reçue
    return data;  // { reponse, audio_url, necessite_consultation }
  } catch (error) {
    console.error("Erreur dans l'appel API : ", error);
    throw new Error("Erreur de connexion à l'API");
  }
};

const ResponseDisplay = () => {
  const { reponse, audio_url, necessite_consultation, loading, error } = useSelector((state) => state.question);
  const [query, setQuery] = useState("");
  const [motif, setMotif] = useState("");
  const [date, setDate] = useState("");
  const dispatch = useDispatch();

  const handleFetchResponse = async () => {
    if (!query.trim()) return;  // Ne pas envoyer la requête si le champ est vide
    dispatch(startLoading());  // Démarrer le chargement
    
    try {
      const data = await fetchResponse(query);  // Appel API simulé
      console.log("Données de l'API : ", data);  // Vérification des données avant de les dispatch
      dispatch(setResponse(data));  // Mise à jour des données
    } catch (err) {
      dispatch(setError(err.message));  // Gérer l'erreur
    }
  };

  const handleConsultation = () => {
    if (motif.trim() && date.trim()) {
      // Logique pour gérer la demande de consultation
      console.log("Consultation demandée avec motif:", motif, "et date:", date);
    }
  };

  return (
    <div className="response-container">
      {loading && <p>Chargement...</p>}
      {error && <p className="error">Erreur : {error}</p>}
      {!loading && !error && reponse ? (
        <div>
          <h3>Réponse :</h3>
          <p>{reponse}</p>
          {audio_url && (
            <audio controls>
              <source src={audio_url} type="audio/mpeg" />
              Votre navigateur ne supporte pas l'audio.
            </audio>
          )}
          {necessite_consultation && (
            <div className="consultation-form">
              <h4>Besoin d'une consultation ?</h4>
              <input
                type="text"
                placeholder="Motif de la consultation"
                value={motif}
                onChange={(e) => setMotif(e.target.value)}
              />
              <input
                type="datetime-local"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
              <button onClick={handleConsultation}>Demander un rendez-vous</button>
            </div>
          )}
        </div>
      ) : (
        <p>Aucune réponse disponible.</p>
      )}

      <div>
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Entrez votre question"
        />
        <button onClick={handleFetchResponse}>Obtenir la réponse</button>
      </div>
    </div>
  );
};

export default ResponseDisplay;
