<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class NewContactMessage extends Notification
{
    use Queueable;

    public $contact;

    public function __construct($contact)
    {
        $this->contact = $contact;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $replyUrl = url('/dashboard/medecin/messages');

        return (new MailMessage)
            ->subject('📬 Nouveau message patient - OphtalmoAI')
            ->greeting('Bonjour Dr. ' . $notifiable->name)
            ->line('Vous avez reçu un nouveau message de :')
            ->line('👤 Nom : ' . $this->contact->name)
            ->line('✉️ Email : ' . $this->contact->email)
            ->line('📞 Téléphone : ' . $this->contact->phone)
            ->line('💬 Message : ' . $this->contact->message)
            ->action('📥 Répondre au message', $replyUrl)
            ->line('Merci de faire confiance à OphtalmoAI.');
    }
}
