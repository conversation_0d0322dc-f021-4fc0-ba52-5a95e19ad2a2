import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { PersonCircle, Envelope, Phone, GeoAlt, Hospital, Award, PencilSquare, Save, XCircle, Upload } from 'react-bootstrap-icons';
import './ProfilMedecin.css';

const ProfilMedecin = () => {
  const [profile, setProfile] = useState({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    adresse: '',
    specialite: 'Ophtalmologie',
    bio: '',
    photo: null
  });
  
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [photoPreview, setPhotoPreview] = useState(null);
  
  // Récupérer les données du profil au chargement
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        const medecinId = localStorage.getItem('medecinId');
        const token = localStorage.getItem('medecinToken');
        
        if (!medecinId && process.env.NODE_ENV === 'production') {
          setError('Identifiant du médecin non trouvé. Veuillez vous reconnecter.');
          setLoading(false);
          return;
        }
        
        try {
          // Essayer d'abord de récupérer les données depuis l'API
          const response = await axios.get(`http://localhost:8000/api/medecins/${medecinId || '1'}/profile`, {
            headers: token ? { Authorization: `Bearer ${token}` } : {}
          });
          
          setProfile(response.data);
          if (response.data.photo) {
            setPhotoPreview(response.data.photo);
          }
        } catch (apiError) {
          console.error('Erreur API:', apiError);
          
          // En développement, utiliser des données fictives
          if (process.env.NODE_ENV !== 'production') {
            const storedProfile = localStorage.getItem('medecinProfile');
            
            if (storedProfile) {
              const parsedProfile = JSON.parse(storedProfile);
              setProfile(parsedProfile);
              if (parsedProfile.photo) {
                setPhotoPreview(parsedProfile.photo);
              }
            } else {
              // Données par défaut
              const defaultProfile = {
                nom: 'Dupont',
                prenom: 'Jean',
                email: '<EMAIL>',
                telephone: '+33 1 23 45 67 89',
                adresse: '123 Rue de la Médecine, 75001 Paris, France',
                specialite: 'Ophtalmologie',
                bio: 'Spécialiste en ophtalmologie avec 15 ans d\'expérience.',
                photo: null
              };
              
              setProfile(defaultProfile);
              localStorage.setItem('medecinProfile', JSON.stringify(defaultProfile));
            }
          } else {
            throw apiError;
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Erreur lors de la récupération du profil:', error);
        setError('Erreur lors de la récupération de votre profil');
        setLoading(false);
      }
    };
    
    fetchProfile();
  }, []);
  
  // Gérer les changements dans le formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfile(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Gérer le téléchargement de photo
  const handlePhotoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result);
        setProfile(prev => ({
          ...prev,
          photo: reader.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Sauvegarder les modifications
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const medecinId = localStorage.getItem('medecinId');
      const token = localStorage.getItem('medecinToken');
      
      try {
        // Essayer d'abord d'envoyer les données à l'API
        await axios.put(`http://localhost:8000/api/medecins/${medecinId || '1'}/profile`, profile, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors de la mise à jour du profil:', apiError);
        
        // En développement, simuler une mise à jour réussie
        if (process.env.NODE_ENV !== 'production') {
          // Stocker le profil mis à jour dans le localStorage
          localStorage.setItem('medecinProfile', JSON.stringify(profile));
          
          // Mettre à jour le nom du médecin dans le localStorage pour l'affichage dans le sidebar
          localStorage.setItem('medecinNom', profile.nom);
        } else {
          throw apiError;
        }
      }
      
      setSuccess(true);
      setEditMode(false);
      
      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      setError('Erreur lors de la mise à jour de votre profil');
    } finally {
      setLoading(false);
    }
  };
  
  // Annuler les modifications
  const handleCancel = () => {
    // Récupérer les données originales
    const storedProfile = localStorage.getItem('medecinProfile');
    if (storedProfile) {
      setProfile(JSON.parse(storedProfile));
      const parsedProfile = JSON.parse(storedProfile);
      if (parsedProfile.photo) {
        setPhotoPreview(parsedProfile.photo);
      } else {
        setPhotoPreview(null);
      }
    }
    
    setEditMode(false);
    setError(null);
  };
  
  if (loading && !profile.nom) {
    return (
      <div className="profile-loading">
        <div className="spinner"></div>
        <p>Chargement de votre profil...</p>
      </div>
    );
  }
  
  return (
    <div className="profile-container">
      <div className="profile-header">
        <h2>Mon Profil</h2>
        {!editMode && (
          <button 
            className="edit-button"
            onClick={() => setEditMode(true)}
          >
            <PencilSquare /> Modifier
          </button>
        )}
      </div>
      
      {error && (
        <div className="profile-error">
          <p>{error}</p>
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}
      
      {success && (
        <div className="profile-success">
          <p>Votre profil a été mis à jour avec succès!</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="profile-form">
        <div className="profile-photo-section">
          <div className="profile-photo">
            {photoPreview ? (
              <img src={photoPreview} alt="Photo de profil" />
            ) : (
              <PersonCircle size={80} />
            )}
            
            {editMode && (
              <div className="photo-upload">
                <label htmlFor="photo-input" className="upload-button">
                  <Upload /> Changer la photo
                </label>
                <input 
                  type="file" 
                  id="photo-input" 
                  accept="image/*" 
                  onChange={handlePhotoChange}
                  style={{ display: 'none' }}
                />
              </div>
            )}
          </div>
          
          <div className="profile-name">
            <h3>{profile.prenom} {profile.nom}</h3>
            <p>{profile.specialite}</p>
          </div>
        </div>
        
        <div className="profile-grid">
          <div className="profile-field">
            <label>
              <PersonCircle /> Prénom
            </label>
            <input 
              type="text" 
              name="prenom" 
              value={profile.prenom} 
              onChange={handleChange}
              readOnly={!editMode}
              required
            />
          </div>
          
          <div className="profile-field">
            <label>
              <PersonCircle /> Nom
            </label>
            <input 
              type="text" 
              name="nom" 
              value={profile.nom} 
              onChange={handleChange}
              readOnly={!editMode}
              required
            />
          </div>
          
          <div className="profile-field">
            <label>
              <Envelope /> Email
            </label>
            <input 
              type="email" 
              name="email" 
              value={profile.email} 
              onChange={handleChange}
              readOnly={!editMode}
              required
            />
          </div>
          
          <div className="profile-field">
            <label>
              <Phone /> Téléphone
            </label>
            <input 
              type="tel" 
              name="telephone" 
              value={profile.telephone} 
              onChange={handleChange}
              readOnly={!editMode}
            />
          </div>
          
          <div className="profile-field">
            <label>
              <Hospital /> Spécialité
            </label>
            <input 
              type="text" 
              name="specialite" 
              value={profile.specialite} 
              onChange={handleChange}
              readOnly={!editMode}
            />
          </div>
          
          <div className="profile-field full-width">
            <label>
              <GeoAlt /> Adresse
            </label>
            <textarea 
              name="adresse" 
              value={profile.adresse} 
              onChange={handleChange}
              readOnly={!editMode}
              rows="2"
            ></textarea>
          </div>
          
          <div className="profile-field full-width">
            <label>
              <Award /> Biographie
            </label>
            <textarea 
              name="bio" 
              value={profile.bio} 
              onChange={handleChange}
              readOnly={!editMode}
              rows="4"
              placeholder={editMode ? "Parlez de votre expérience, vos spécialités..." : ""}
            ></textarea>
          </div>
        </div>
        
        {editMode && (
          <div className="profile-actions">
            <button 
              type="button" 
              className="cancel-button"
              onClick={handleCancel}
            >
              <XCircle /> Annuler
            </button>
            <button 
              type="submit" 
              className="save-button"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-small"></span>
                  Enregistrement...
                </>
              ) : (
                <>
                  <Save /> Enregistrer
                </>
              )}
            </button>
          </div>
        )}
      </form>
    </div>
  );
};

export default ProfilMedecin;
