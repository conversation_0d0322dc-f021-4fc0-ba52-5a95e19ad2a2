.dashboard {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
}

.sidebar {
  width: 280px;
  background: #ffffff;
  padding: 24px;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 40px;
}

.logo-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.logo h4 {
  color: #006fac;
  font-weight: 600;
  margin: 0;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px;
  color: #4a5568;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item a {
  display: flex;
  align-items: center;
  gap: 12px;
  color: inherit;
  text-decoration: none;
  width: 100%;
}

.nav-item.active a {
  color: #006fac;
}

.nav-item a.active {
  background: #e0f2fe;
  color: #006fac;
  font-weight: 500;
  border-radius: 8px;
}

.nav-item:hover {
  background: #f0f7ff;
  color: #006fac;
  transform: translateX(5px);
}

.nav-item.active {
  background: #e0f2fe;
  color: #006fac;
  font-weight: 600;
  border-left: 3px solid #006fac;
}

.sidebar-footer {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a5568;
  font-size: 0.9rem;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #fff3f3;
  color: #ff4757;
  border: none;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-weight: 500;
}

.logout-button:hover {
  background-color: #ffe0e0;
  transform: translateY(-2px);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px;
  margin-bottom: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #2d3748;
  font-weight: 500;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 32px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.dashboard-title h2 {
  color: #006fac;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 5px;
}

.dashboard-title p {
  color: #718096;
  margin: 0;
  font-size: 14px;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-header {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.welcome-header h1 {
  color: #2d3748;
  margin: 0;
  font-size: 1.8rem;
}

.date-display {
  color: #718096;
  margin-top: 8px;
}

.quick-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-card.blue .stat-icon { background: #e0f2fe; color: #006fac; }
.stat-card.orange .stat-icon { background: #fff3e0; color: #ed8936; }
.stat-card.green .stat-icon { background: #f0fff4; color: #48bb78; }
.stat-card.purple .stat-icon { background: #faf5ff; color: #805ad5; }

.stat-info h3 {
  font-size: 0.9rem;
  color: #718096;
  margin: 0;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.charts-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.chart-card h3 {
  color: #2d3748;
  margin-bottom: 16px;
}

.recent-activity {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.activity-timeline {
  position: relative;
  padding-left: 24px;
}

.activity-item {
  position: relative;
  padding-bottom: 24px;
}

.activity-dot {
  position: absolute;
  left: -24px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #006fac;
  border: 3px solid #e0f2fe;
}

.activity-content p {
  margin: 0;
  color: #2d3748;
}

.activity-content small {
  color: #718096;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Animation */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f7ff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #e0f2fe;
  border-top: 5px solid #006fac;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }

  .main-content {
    margin-left: 0;
  }

  .quick-stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard {
    background: #1a1a1a;
  }

  .sidebar {
    background: #2d2d2d;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
  }

  .nav-item {
    color: #e5e7eb;
  }

  .nav-item:hover {
    background: #3d3d3d;
    color: #60a5fa;
  }

  .nav-item.active {
    background: #374151;
    color: #60a5fa;
  }

  .main-content {
    background: #1a1a1a;
  }

  .welcome-header,
  .stat-card,
  .chart-container {
    background: #2d2d2d;
    color: #e5e7eb;
  }

  .stat-card h3 {
    color: #9ca3af;
  }

  .stat-card .value {
    color: #e5e7eb;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chart Customization */
.chart-container canvas {
  max-width: 100%;
  height: auto !important;
}

/* Hover Effects */
.nav-item,
.stat-card,
.chart-container {
  transition: all 0.3s ease;
}

.nav-item:hover,
.stat-card:hover {
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}





