import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Bell } from 'react-bootstrap-icons';

const NotificationsPanel = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasNewNotifications, setHasNewNotifications] = useState(false);

  const fetchNotifications = async () => {
    try {
      setLoading(true);

      // En développement, vérifier d'abord s'il y a des notifications dans le localStorage
      if (process.env.NODE_ENV !== 'production') {
        const storedNotifs = localStorage.getItem('mockNotifications');

        if (storedNotifs) {
          const mockNotifications = JSON.parse(storedNotifs);
          console.log("Notifications récupérées du localStorage:", mockNotifications);

          setNotifications(mockNotifications);
          setHasNewNotifications(mockNotifications.filter(n => !n.lu).length > 0);
          setError(null);
          setLoading(false);
          return;
        }
      }

      // Si pas de notifications dans le localStorage ou en production, essayer l'API
      const medecinId = localStorage.getItem('medecinId') || '1'; // ID par défaut pour le développement
      const token = localStorage.getItem('medecinToken');

      // En environnement de production, nous voulons toujours vérifier l'ID
      if (process.env.NODE_ENV === 'production' && !localStorage.getItem('medecinId')) {
        setError('Identifiant du médecin non trouvé. Veuillez vous reconnecter.');
        setLoading(false);
        return;
      }

      try {
        // Essayer de récupérer les notifications depuis l'API
        const response = await axios.get(`http://localhost:8000/api/medecins/${medecinId}/notifications`, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });

        const newNotifications = response.data;
        setNotifications(newNotifications);

        // Vérifier s'il y a de nouvelles notifications non lues
        const unreadCount = newNotifications.filter(n => !n.lu).length;
        setHasNewNotifications(unreadCount > 0);

        setError(null);
      } catch (apiError) {
        console.error('Erreur API:', apiError);

        // Si l'API échoue, utiliser des données de test en développement
        if (process.env.NODE_ENV !== 'production') {
          // Vérifier s'il y a des notifications simulées dans le localStorage
          const storedNotifs = localStorage.getItem('mockNotifications');
          let mockNotifications;

          if (storedNotifs) {
            // Utiliser les notifications stockées dans le localStorage
            mockNotifications = JSON.parse(storedNotifs);
            console.log("Notifications récupérées du localStorage:", mockNotifications);
          } else {
            // Utiliser des données fictives par défaut
            mockNotifications = [
              {
                id: 1,
                titre: 'Nouvelle question',
                contenu: 'Un patient a posé une question sur le glaucome.',
                lu: false,
                created_at: new Date().toISOString()
              },
              {
                id: 2,
                titre: 'Rappel de rendez-vous',
                contenu: 'Vous avez une consultation demain à 14h00.',
                lu: true,
                created_at: new Date(Date.now() - 86400000).toISOString() // Hier
              },
              {
                id: 3,
                titre: 'Demande de consultation',
                contenu: 'Patient: Marie Dupont - Motif: Suivi post-opératoire',
                lu: false,
                created_at: new Date(Date.now() - 43200000).toISOString(), // 12 heures
                type: 'consultation'
              }
            ];

            // Stocker ces notifications par défaut dans le localStorage
            localStorage.setItem('mockNotifications', JSON.stringify(mockNotifications));
          }

          setNotifications(mockNotifications);
          setHasNewNotifications(mockNotifications.filter(n => !n.lu).length > 0);
          setError(null);
        } else {
          throw apiError; // En production, propager l'erreur
        }
      }
    } catch (error) {
      setError('Erreur lors de la récupération des notifications');
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      const token = localStorage.getItem('medecinToken');

      try {
        // Essayer d'abord d'appeler l'API
        await axios.patch(`http://localhost:8000/api/notifications/${notificationId}/mark-read`, {}, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors du marquage comme lu:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif =>
              notif.id === notificationId ? { ...notif, lu: true } : notif
            );

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Notification marquée comme lue dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Mettre à jour l'état local même si l'API échoue (en développement)
      setNotifications(notifications.map(notif =>
        notif.id === notificationId ? { ...notif, lu: true } : notif
      ));

      // Mettre à jour le statut des nouvelles notifications
      const remainingUnread = notifications.filter(n => !n.lu && n.id !== notificationId).length;
      setHasNewNotifications(remainingUnread > 0);
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
      setError('Erreur lors du marquage de la notification comme lue');
    }
  };

  const markAllAsRead = async () => {
    try {
      const medecinId = localStorage.getItem('medecinId') || '1'; // ID par défaut pour le développement
      const token = localStorage.getItem('medecinToken');

      // En production, vérifier l'ID du médecin
      if (process.env.NODE_ENV === 'production' && !localStorage.getItem('medecinId')) {
        setError('Identifiant du médecin non trouvé. Veuillez vous reconnecter.');
        return;
      }

      try {
        // Essayer d'abord d'appeler l'API
        await axios.patch(`http://localhost:8000/api/medecins/${medecinId}/notifications/mark-all-read`, {}, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors du marquage de toutes les notifications:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif => ({ ...notif, lu: true }));

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Toutes les notifications marquées comme lues dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Mettre à jour l'état local même si l'API échoue (en développement)
      setNotifications(notifications.map(notif => ({ ...notif, lu: true })));
      setHasNewNotifications(false);
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les notifications:', error);
      setError('Erreur lors du marquage des notifications comme lues');
    }
  };

  // Fonction pour créer une notification de test
  const createTestNotification = (count = 1) => {
    try {
      // Récupérer les notifications existantes
      const storedNotifs = localStorage.getItem('mockNotifications');
      let mockNotifications = [];

      if (storedNotifs) {
        mockNotifications = JSON.parse(storedNotifs);
      }

      // Générer un ID unique
      const maxId = mockNotifications.length > 0
        ? Math.max(...mockNotifications.map(n => n.id))
        : 0;

      const newNotifications = [];

      // Types de notifications possibles
      const notificationTypes = [
        {
          titre: 'Nouvelle question',
          contenu: 'Un patient a posé une question sur la rétinopathie diabétique.',
          type: 'question'
        },
        {
          titre: 'Demande de consultation',
          contenu: 'Patient: Jean Dupont - Motif: Douleurs oculaires persistantes',
          type: 'consultation'
        },
        {
          titre: 'Rappel de rendez-vous',
          contenu: 'Vous avez une consultation demain à 14h00 avec Mme Martin.',
          type: 'rappel'
        },
        {
          titre: 'Nouvelle notification de test',
          contenu: 'Cette notification a été créée pour tester le système de notifications.',
          type: 'test'
        }
      ];

      // Créer le nombre demandé de notifications
      for (let i = 0; i < count; i++) {
        // Choisir un type de notification aléatoire
        const randomType = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];

        // Créer une nouvelle notification
        const newNotification = {
          id: maxId + i + 1,
          titre: randomType.titre,
          contenu: randomType.contenu,
          lu: false,
          created_at: new Date().toISOString(),
          type: randomType.type
        };

        // Ajouter la notification à la liste des nouvelles notifications
        newNotifications.push(newNotification);
      }

      // Ajouter les notifications aux notifications existantes
      const updatedNotifications = [...mockNotifications, ...newNotifications];

      // Mettre à jour le localStorage
      localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));

      // Mettre à jour l'état local
      setNotifications(updatedNotifications);
      setHasNewNotifications(true);

      // Afficher un message de succès
      alert(`${count} notification(s) de test créée(s) avec succès !`);
    } catch (error) {
      console.error('Erreur lors de la création des notifications de test:', error);
      setError('Erreur lors de la création des notifications de test');
    }
  };

  // Fonction pour créer plusieurs notifications de test
  const createMultipleTestNotifications = () => {
    createTestNotification(5);
  };

  const deleteNotification = async (notificationId) => {
    try {
      const token = localStorage.getItem('medecinToken');

      try {
        // Essayer d'abord d'appeler l'API
        await axios.delete(`http://localhost:8000/api/notifications/${notificationId}`, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors de la suppression de la notification:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.filter(notif => notif.id !== notificationId);

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Notification supprimée dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Supprimer la notification de l'état local même si l'API échoue (en développement)
      setNotifications(notifications.filter(notif => notif.id !== notificationId));

      // Mettre à jour le statut des nouvelles notifications
      const remainingUnread = notifications.filter(n => !n.lu && n.id !== notificationId).length;
      setHasNewNotifications(remainingUnread > 0);
    } catch (error) {
      console.error('Erreur lors de la suppression de la notification:', error);
      setError('Erreur lors de la suppression de la notification');
    }
  };

  useEffect(() => {
    fetchNotifications();

    // Rafraîchir les notifications toutes les 60 secondes uniquement en production
    let interval;
    if (process.env.NODE_ENV === 'production') {
      interval = setInterval(fetchNotifications, 60000);
    }

    // Nettoyage à la désinscription du composant
    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  if (loading) {
    return (
      <div style={styles.loadingContainer}>
        <div style={styles.loadingSpinner}></div>
        <p>Chargement des notifications...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={styles.errorContainer}>
        <p style={styles.errorText}>{error}</p>
        <button onClick={fetchNotifications} style={styles.retryButton}>
          Réessayer
        </button>
      </div>
    );
  }

  const unreadCount = notifications.filter(n => !n.lu).length;

  return (
    <div style={styles.panel}>
      <div style={styles.header}>
        <div>
          <h3 style={styles.title}>
            <Bell style={styles.bellIcon} className={hasNewNotifications ? "bell-animation" : ""} />
            Centre de Notifications
            {unreadCount > 0 && (
              <span style={styles.badge}>{unreadCount}</span>
            )}
          </h3>
          <p style={styles.subtitle}>Gérez toutes vos notifications et demandes de consultation</p>
        </div>
        <div style={styles.headerButtons}>
          <button
            onClick={fetchNotifications}
            style={styles.refreshButton}
            title="Rafraîchir les notifications"
          >
            ↻
          </button>
          <button
            onClick={() => createTestNotification(1)}
            style={styles.testButton}
            title="Créer une notification de test"
          >
            + Test
          </button>
          <button
            onClick={createMultipleTestNotifications}
            style={styles.multiTestButton}
            title="Créer 5 notifications de test"
          >
            + 5 Tests
          </button>
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              style={styles.markAllButton}
              title="Tout marquer comme lu"
            >
              Tout marquer comme lu
            </button>
          )}
        </div>
      </div>

      {notifications.length === 0 ? (
        <div style={styles.emptyState}>
          <Bell size={40} style={{ color: '#b2bec3', marginBottom: '15px' }} />
          <p>Aucune notification</p>
        </div>
      ) : (
        <div style={styles.notificationList}>
          {notifications.map((notif) => (
            <div
              key={notif.id}
              style={{
                ...styles.notification,
                backgroundColor: notif.lu ? '#f0f8ff' : '#def3ff',
                borderLeft: notif.lu ? '3px solid #b2bec3' : '3px solid #008bd5',
              }}
            >
              {!notif.lu && <div style={styles.unreadIndicator}></div>}
              <div style={styles.notificationContent}>
                <div style={styles.notificationHeader}>
                  <h4 style={styles.notificationTitle}>{notif.titre}</h4>
                  <div style={styles.notificationActions}>
                    {!notif.lu && (
                      <button
                        onClick={() => markAsRead(notif.id)}
                        style={styles.actionButton}
                        title="Marquer comme lu"
                      >
                        ✓
                      </button>
                    )}
                    <button
                      onClick={() => deleteNotification(notif.id)}
                      style={{...styles.actionButton, ...styles.deleteButton}}
                      title="Supprimer"
                    >
                      ×
                    </button>
                  </div>
                </div>
                <p style={styles.notificationText}>{notif.contenu}</p>
                <div style={styles.notificationMeta}>
                  {notif.created_at && (
                    <span style={styles.timestamp}>
                      {new Date(notif.created_at).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {error && (
        <div style={styles.errorBanner}>
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            style={styles.closeErrorButton}
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};

const styles = {
  panel: {
    backgroundColor: '#fff',
    padding: '20px',
    borderRadius: '12px',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    maxWidth: '100%',
    margin: '0 auto 20px',
    position: 'relative',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '20px',
    borderBottom: '1px solid #e1e1e1',
    paddingBottom: '15px',
  },
  title: {
    color: '#006fac',
    fontSize: '24px',
    fontWeight: '600',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    margin: 0,
    marginBottom: '5px',
  },
  bellIcon: {
    color: '#006fac',
    marginRight: '5px',
  },
  subtitle: {
    color: '#718096',
    margin: '0 0 10px 0',
    fontSize: '14px',
  },
  badge: {
    backgroundColor: '#ff4757',
    color: 'white',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '14px',
    marginLeft: '8px',
  },
  headerButtons: {
    display: 'flex',
    gap: '10px',
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: 'transparent',
    color: '#006fac',
    border: '1px solid #006fac',
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    fontSize: '18px',
    transition: 'all 0.3s ease',
  },
  notificationList: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  },
  notification: {
    border: '1px solid #e1e1e1',
    borderRadius: '8px',
    padding: '16px',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',
  },
  unreadIndicator: {
    position: 'absolute',
    top: '16px',
    right: '16px',
    width: '10px',
    height: '10px',
    borderRadius: '50%',
    backgroundColor: '#008bd5',
  },
  notificationContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
  },
  notificationHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  notificationTitle: {
    margin: 0,
    fontSize: '18px',
    fontWeight: '600',
    color: '#2d3436',
  },
  notificationActions: {
    display: 'flex',
    gap: '5px',
  },
  actionButton: {
    backgroundColor: 'transparent',
    border: '1px solid #ddd',
    borderRadius: '50%',
    width: '28px',
    height: '28px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    fontSize: '16px',
    transition: 'all 0.2s ease',
    color: '#008bd5',
  },
  deleteButton: {
    color: '#ff4757',
    borderColor: '#ffccd5',
  },
  notificationText: {
    margin: 0,
    color: '#636e72',
    lineHeight: '1.5',
  },
  notificationMeta: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '8px',
  },
  timestamp: {
    color: '#b2bec3',
    fontSize: '14px',
  },
  markAllButton: {
    backgroundColor: 'transparent',
    color: '#008bd5',
    border: '1px solid #008bd5',
    borderRadius: '6px',
    padding: '8px 16px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.3s ease',
  },
  testButton: {
    backgroundColor: '#e6f7ff',
    color: '#008bd5',
    border: '1px solid #008bd5',
    borderRadius: '6px',
    padding: '8px 16px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.3s ease',
  },
  loadingContainer: {
    textAlign: 'center',
    padding: '40px',
  },
  loadingSpinner: {
    border: '4px solid #f3f3f3',
    borderTop: '4px solid #008bd5',
    borderRadius: '50%',
    width: '40px',
    height: '40px',
    animation: 'spin 1s linear infinite',
    margin: '0 auto 20px',
  },
  errorContainer: {
    textAlign: 'center',
    padding: '40px',
    backgroundColor: '#fff3f3',
    borderRadius: '8px',
  },
  errorText: {
    color: '#ff4757',
    marginBottom: '20px',
  },
  retryButton: {
    backgroundColor: '#ff4757',
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    padding: '8px 16px',
    cursor: 'pointer',
  },
  emptyState: {
    textAlign: 'center',
    padding: '40px',
    color: '#b2bec3',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorBanner: {
    position: 'absolute',
    bottom: '20px',
    left: '20px',
    right: '20px',
    backgroundColor: '#fff3f3',
    borderLeft: '4px solid #ff4757',
    padding: '12px 16px',
    borderRadius: '8px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    zIndex: 10,
  },
  closeErrorButton: {
    background: 'none',
    border: 'none',
    color: '#ff4757',
    fontSize: '20px',
    cursor: 'pointer',
  },
};

// Ajout d'une feuille de style pour l'animation de la cloche
document.addEventListener('DOMContentLoaded', () => {
  const styleSheet = document.createElement("style");
  // La propriété type="text/css" est dépréciée, nous l'omettons
  styleSheet.textContent = `
    @keyframes bellRing {
      0% { transform: rotate(0); }
      10% { transform: rotate(15deg); }
      20% { transform: rotate(-15deg); }
      30% { transform: rotate(10deg); }
      40% { transform: rotate(-10deg); }
      50% { transform: rotate(5deg); }
      60% { transform: rotate(-5deg); }
      70% { transform: rotate(0); }
      100% { transform: rotate(0); }
    }

    .bell-animation {
      animation: bellRing 1s ease-in-out;
      animation-iteration-count: 2;
    }
  `;
  document.head.appendChild(styleSheet);
});

export default NotificationsPanel;
