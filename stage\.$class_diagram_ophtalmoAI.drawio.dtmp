<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.0.16 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36" version="26.0.16">
  <diagram id="R3pCu6saTDH2KjDHg8fF" name="Page-1">
    <mxGraphModel dx="1020" dy="504" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Utilisateur" style="shape=swimlane;" parent="1" vertex="1">
          <mxGeometry x="50" y="50" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="3" value="ID:int&lt;div&gt;&amp;nbsp;Nom:Varchar(30)&lt;/div&gt;&lt;div&gt;&amp;nbsp;Email Mot de passe:varchar(30)&amp;nbsp;&lt;/div&gt;&lt;div&gt;Role:varchat(30)&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;" parent="2" vertex="1">
          <mxGeometry x="10" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Question" style="shape=swimlane;" parent="1" vertex="1">
          <mxGeometry x="300" y="50" width="200" height="128" as="geometry">
            <mxRectangle x="300" y="50" width="90" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="BMaCJwWD-NqN5QqYx6wH-12" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;id:int&lt;/font&gt;&lt;div&gt;&lt;font size=&quot;3&quot;&gt;texte:varchat(300)&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font size=&quot;3&quot;&gt;langue:varchar(4)&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font size=&quot;3&quot;&gt;date;date&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="4" vertex="1">
          <mxGeometry x="5" y="38" width="150" height="90" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Réponse" style="shape=swimlane;" parent="1" vertex="1">
          <mxGeometry x="550" y="50" width="220" height="140" as="geometry" />
        </mxCell>
        <mxCell id="5" value="ID: int&lt;div&gt;&amp;nbsp;UtilisateurID :foreing key&lt;/div&gt;&lt;div&gt;Texte:varchat(400)&lt;/div&gt;&lt;div&gt;audio:link&lt;/div&gt;&lt;div&gt;&amp;nbsp;Date:date()&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;" parent="6" vertex="1">
          <mxGeometry x="20" y="60" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Catégorie" style="shape=swimlane;" parent="1" vertex="1">
          <mxGeometry x="800" y="50" width="240" height="120" as="geometry" />
        </mxCell>
        <mxCell id="9" value="ID Nom" style="text;html=1;whiteSpace=wrap;" parent="8" vertex="1">
          <mxGeometry x="800" y="80" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="7" value="ID:int&lt;div&gt;&amp;nbsp;QuestionID :foreing key&lt;/div&gt;&lt;div&gt;Texte:varchat(10)&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;" parent="8" vertex="1">
          <mxGeometry x="40" y="40" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="10" parent="1" source="2" target="4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="11" parent="1" source="4" target="6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="12" parent="1" source="8" target="4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
