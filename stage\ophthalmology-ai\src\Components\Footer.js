import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Eye, Mail, MapPin, Phone } from 'lucide-react';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../styles/Footer.css'; 

const Footer = () => {
  return (
    <footer className="footer-section mt-5 pt-5">
      <div className="container">
        <div className="row text-md-start text-center">
          <div className="col-md-4 mb-4">
            <h5 className="footer-title d-flex align-items-center">
              <Eye className="me-2" size={24} />
              À propos d'OphtalmoAI
            </h5>
            <p className="footer-text">
              Révolutionner l'ophtalmologie avec l'IA pour une assistance rapide et intelligente.
            </p>
          </div>

          <div className="col-md-4 mb-4">
            <h5 className="footer-title">Liens rapides</h5>
            <ul className="list-unstyled footer-links">
              <li><Link to="/">Accueil</Link></li>
              <li><Link to="/a-propos">À propos</Link></li>
              <li><Link to="/categories">Catégories</Link></li>
              <li><Link to="/contact">Contact</Link></li>
              <li><Link to="/login-medecin">Espace Médecin</Link></li>
            </ul>
          </div>

          <div className="col-md-4 mb-4">
            <h5 className="footer-title">Contact</h5>
            <ul className="list-unstyled footer-contact">
              <li>
                <MapPin className="me-2" size={18} />
                Rabat, Maroc
              </li>
              <li>
                <Phone className="me-2" size={18} />
                +212 600 000 000
              </li>
              <li>
                <Mail className="me-2" size={18} />
                <EMAIL>
              </li>
            </ul>
          </div>
        </div>

        <div className="text-center pt-3 mt-4 border-top">
          <p className="footer-bottom-text">
            &copy; {new Date().getFullYear()} OphtalmoAI. Tous droits réservés.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
