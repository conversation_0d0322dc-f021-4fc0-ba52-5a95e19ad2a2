<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use Illuminate\Http\Request;

class PatientController extends Controller
{
    public function index()
    {
        return response()->json(Patient::all(), 200);
    }

    public function store(Request $request)
    {
        $patient = Patient::create($request->validate([
            'nom' => 'required|string',
            'email' => 'required|email|unique:patients'
        ]));

        return response()->json($patient, 201);
    }
}
