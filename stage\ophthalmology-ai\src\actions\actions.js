// export const setQuestions = (questions) => ({
//   type: 'SET_QUESTIONS',
//   payload: questions,
// });

// export const setFilteredQuestions = (filteredQuestions) => ({
//   type: 'SET_FILTERED_QUESTIONS',
//   payload: filteredQuestions,
// });

// export const setLoading = (loading) => ({
//   type: 'SET_LOADING',
//   payload: loading,
// });
dispatch({ type: 'SET_LOADING', payload: true });
dispatch({ type: 'SET_QUESTIONS', payload: data });
dispatch({ type: 'SET_FILTERED_QUESTIONS', payload: filtered });
dispatch({ type: 'CLEAR_RESPONSE' });
dispatch({ type: 'SET_ERROR', payload: "Erreur lors de la récupération" });
