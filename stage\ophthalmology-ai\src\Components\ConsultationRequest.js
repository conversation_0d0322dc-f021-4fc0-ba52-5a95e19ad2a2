import React, { useState } from 'react';
import axios from 'axios';
import { Calendar, Clock, Phone, User, FileText, Send, X, Mail, MessageSquare } from 'lucide-react';

const ConsultationRequest = ({ onClose, specialite = 'Ophtalmologue' }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    motif: '',
    date_preferee: '',
    heure_preferee: '',
    message: ''
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      console.log("Envoi de la demande de consultation:", formData);

      // En mode développement, simuler une réponse réussie si l'API n'est pas disponible
      try {
        // Appel à l'API pour envoyer la demande de consultation
        const response = await axios.post('http://localhost:8000/api/consultations/demande', {
          ...formData,
          specialite
        });

        console.log("Réponse de l'API:", response.data);

        // Créer manuellement une notification pour le médecin (pour le développement)
        try {
          // Récupérer l'ID du médecin (utiliser '1' par défaut en développement)
          const medecinId = localStorage.getItem('medecinId') || '1';

          // Créer une notification pour le médecin
          await axios.post('http://localhost:8000/api/notifications/create', {
            medecin_id: medecinId,
            titre: `Nouvelle demande de consultation - ${formData.motif}`,
            contenu: `Patient: ${formData.prenom} ${formData.nom} - Téléphone: ${formData.telephone} - Email: ${formData.email} - Motif: ${formData.motif}`,
            type: 'consultation'
          });

          console.log("Notification créée avec succès");
        } catch (notifError) {
          console.error("Erreur lors de la création de la notification:", notifError);
          // Continuer même si la création de notification échoue
        }

      } catch (apiError) {
        console.error("Erreur API:", apiError);

        // En développement, simuler une réponse réussie
        if (process.env.NODE_ENV !== 'production') {
          console.log("Mode développement: simulation d'une réponse réussie");

          // Créer une entrée dans le localStorage pour simuler une notification
          const existingNotifs = JSON.parse(localStorage.getItem('mockNotifications') || '[]');
          const newNotif = {
            id: Date.now(),
            titre: `Nouvelle demande de consultation - ${formData.motif}`,
            contenu: `Patient: ${formData.prenom} ${formData.nom} - Téléphone: ${formData.telephone} - Email: ${formData.email} - Motif: ${formData.motif}`,
            lu: false,
            created_at: new Date().toISOString(),
            type: 'consultation'
          };

          localStorage.setItem('mockNotifications', JSON.stringify([newNotif, ...existingNotifs]));
          console.log("Notification simulée créée dans localStorage");
        } else {
          throw apiError; // En production, propager l'erreur
        }
      }

      setSuccess(true);
      setLoading(false);

      // Réinitialiser le formulaire après 3 secondes
      setTimeout(() => {
        if (onClose) {
          onClose();
        }
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la demande:', error);
      setError('Une erreur est survenue lors de l\'envoi de votre demande. Veuillez réessayer.');
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="consultation-success">
        <div className="success-icon">✓</div>
        <h4>Demande envoyée avec succès!</h4>
        <p>Nous avons bien reçu votre demande de consultation. Un médecin vous contactera prochainement.</p>
        <button className="btn btn-primary mt-3" onClick={onClose}>Fermer</button>
      </div>
    );
  }

  return (
    <div className="consultation-request-form">
      <div className="form-header">
        <h4>Demande de consultation - {specialite}</h4>
        {onClose && (
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        )}
      </div>

      {error && (
        <div className="alert alert-danger">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label>
              <User size={16} className="icon" />
              Nom
            </label>
            <input
              type="text"
              name="nom"
              value={formData.nom}
              onChange={handleChange}
              required
              placeholder="Votre nom"
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label>
              <User size={16} className="icon" />
              Prénom
            </label>
            <input
              type="text"
              name="prenom"
              value={formData.prenom}
              onChange={handleChange}
              required
              placeholder="Votre prénom"
              className="form-control"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>
              <Mail size={16} className="icon" />
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="Votre email"
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label>
              <Phone size={16} className="icon" />
              Téléphone
            </label>
            <input
              type="tel"
              name="telephone"
              value={formData.telephone}
              onChange={handleChange}
              required
              placeholder="Votre numéro de téléphone"
              className="form-control"
            />
          </div>
        </div>

        <div className="form-group">
          <label>
            <FileText size={16} className="icon" />
            Motif de consultation
          </label>
          <input
            type="text"
            name="motif"
            value={formData.motif}
            onChange={handleChange}
            required
            placeholder="Ex: Douleur à l'œil, vision floue..."
            className="form-control"
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>
              <Calendar size={16} className="icon" />
              Date préférée
            </label>
            <input
              type="date"
              name="date_preferee"
              value={formData.date_preferee}
              onChange={handleChange}
              min={new Date().toISOString().split('T')[0]}
              className="form-control"
            />
          </div>

          <div className="form-group">
            <label>
              <Clock size={16} className="icon" />
              Heure préférée
            </label>
            <input
              type="time"
              name="heure_preferee"
              value={formData.heure_preferee}
              onChange={handleChange}
              className="form-control"
            />
          </div>
        </div>

        <div className="form-group">
          <label>
            <MessageSquare size={16} className="icon" />
            Message complémentaire
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder="Informations complémentaires pour le médecin..."
            className="form-control"
            rows="3"
          ></textarea>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Envoi en cours...
              </>
            ) : (
              <>
                <Send size={16} className="me-2" />
                Envoyer ma demande
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ConsultationRequest;
