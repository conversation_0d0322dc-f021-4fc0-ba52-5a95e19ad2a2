

/* Variables globales */
:root {
  --primary-gradient: linear-gradient(135deg, #74d8ff 0%, #008bd5 100%);
  --secondary-gradient: linear-gradient(135deg, #def3ff 0%, #74d8ff 100%);
  --accent-color: #ff6b6b;
  --shadow-soft: 0 4px 6px -1px rgba(0, 111, 172, 0.1), 0 2px 4px -1px rgba(0, 111, 172, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 111, 172, 0.1), 0 4px 6px -2px rgba(0, 111, 172, 0.05);
  --transition-smooth: all 0.3s ease-in-out;
}

/* Styles généraux */
body {
  background: linear-gradient(135deg, #eff9ff 0%, #ffffff 100%);
  min-height: 100vh;
}

/* SearchBar2 Styles */
.search-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #eff9ff 0%, #ffffff 100%);
}

.search-hero {
  min-height: 60vh;
  background: var(--primary-gradient);
  padding: 4rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

.search-content {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.search-title {
  color: white;
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  width: 100%;
  margin-bottom: 2rem;
}

.search-input-group {
  display: flex;
  gap: 1rem;
  background: white;
  padding: 0.5rem;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  border: none;
  padding: 1.2rem;
  font-size: 1.1rem;
  border-radius: 10px;
  background: transparent;
}

.search-input:focus {
  outline: none;
}

.search-button {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: transform 0.2s;
}

.search-button:hover:not(:disabled) {
  transform: translateY(-2px);
}

.search-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-icon {
  width: 20px;
  height: 20px;
}

.suggestions-dropdown {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
  overflow: hidden;
}

.suggestion-item {
  width: 100%;
  padding: 1rem;
  border: none;
  background: transparent;
  text-align: left;
  transition: background 0.2s;
}

.suggestion-item:hover {
  background: #f0f9ff;
}

.search-features {
  max-width: 1200px;
  margin: -50px auto 0;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.popular-suggestions {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.popular-suggestions h5 {
  color: #006fac;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.suggestion-pill {
  background: #f0f9ff;
  color: #006fac;
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-pill:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-2px);
}

.daily-suggestion {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.suggestion-icon {
  width: 40px;
  height: 40px;
  color: #006fac;
}

.daily-suggestion strong {
  display: block;
  color: #006fac;
  margin-bottom: 0.5rem;
}

.daily-suggestion p {
  margin: 0;
  color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-hero {
    min-height: 70vh;
    padding: 2rem 1rem;
  }

  .search-title {
    font-size: 2rem;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-button {
    width: 100%;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .daily-suggestion {
    flex-direction: column;
    text-align: center;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Modal Styles */
.custom-modal {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  border: none;
  border-radius: 20px;
  overflow: hidden;
}

.modal-header {
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.modal-body {
  padding: 2rem;
}

.consultation-message {
  background: #f0f9ff;
  border-radius: 15px;
  padding: 2rem;
}

/* Boutons du modal */
.modal-action-button {
  border-radius: 12px;
  padding: 0.8rem 1.5rem;
  font-weight: 500;
  transition: var(--transition-smooth);
}

.modal-action-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-soft);
}

/* Suggestion du jour */
.suggestion-of-day {
  background: linear-gradient(135deg, #ffffff 0%, #eff9ff 100%);
  border-left: 4px solid #006fac;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: var(--shadow-soft);
}

.suggestion-of-day strong {
  color: #006fac;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-title {
    font-size: 1.8rem;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-button {
    width: 100%;
    margin-top: 1rem;
  }

  .suggestion-pill {
    width: 100%;
    text-align: center;
    margin-bottom: 0.5rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}
/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Navbar Styles */
.navbar {
  background-color: white !important;
  border-bottom: 1px solid rgba(0, 111, 172, 0.1);
  padding: 0.5rem 0;
  box-shadow: 0 2px 10px rgba(0, 139, 213, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-brand {
  font-size: 1.8rem;
  font-weight: 700;
  color: #006fac !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-nav {
  gap: 0.5rem;
}

.nav-link {
  color: #054e75 !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.nav-link.active {
  color: #006fac !important;
  background-color: #e0f2fe;
  font-weight: 600;
}

/* Suppression des effets de survol */
.nav-link:hover {
  color: #054e75 !important;
  transform: none;
  background-color: transparent;
}

/* Style du bouton hamburger */
.navbar-toggler {
  border: 1px solid rgba(0, 111, 172, 0.2);
  padding: 0.4rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .navbar-nav {
    padding: 1rem 0;
  }

  .nav-link {
    padding: 0.5rem 0 !important;
  }

  .navbar-collapse {
    background-color: white;
    padding: 1rem;
  }
}

/* Footer Styles */
.footer-section {
  background: linear-gradient(to bottom, #ffffff, #f0f9ff);
  padding-top: 4rem;
  padding-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.footer-title {
  color: #006fac;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-text {
  color: #054e75;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-links a {
  color: #054e75;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-links a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #006fac;
  transition: width 0.3s ease;
}

.footer-links a:hover {
  color: #006fac;
  transform: translateX(5px);
}

.footer-links a:hover::after {
  width: 100%;
}

.footer-contact li {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #054e75;
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.footer-contact li:hover {
  transform: translateX(5px);
}

.footer-contact svg {
  color: #006fac;
}

.footer-bottom-text {
  color: #054e75;
  font-size: 0.9rem;
  margin: 0;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 111, 172, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }

  .navbar-brand {
    font-size: 1.5rem;
  }

  .navbar-toggler {
    border-color: #006fac;
    padding: 0.4rem;
  }

  .navbar-toggler:focus {
    box-shadow: none;
    border-color: #008bd5;
  }

  .navbar-nav {
    gap: 0.5rem;
    padding: 1rem 0;
  }

  .nav-link {
    text-align: center;
  }

  .footer-section {
    padding-top: 2rem;
    text-align: center;
  }

  .footer-title {
    justify-content: center;
  }

  .footer-contact li {
    justify-content: center;
  }

  .footer-links {
    align-items: center;
  }
}

/* Animation pour le hover des éléments du footer */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.footer-contact li:hover svg {
  animation: float 2s ease-in-out infinite;
}

/* About Page */
.container img {
  max-width: 100%;
  height: auto;
}

.stars {
  color: var(--blue-whale-400);
}

/* Contact Page */
form input, form textarea {
  width: 100%;
  margin-bottom: 1rem;
  padding: 0.5rem;
  border: 1px solid var(--blue-whale-300);
  border-radius: 5px;
}

form button {
  background-color: var(--blue-whale-500);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

form button:hover {
  background-color: var(--blue-whale-700);
}

/* Categories Page */
#categories {
  background-color: var(--blue-whale-100);
  padding: 2rem;
}

#categories ul {
  list-style: disc;
  margin-left: 2rem;
}

/* Search Bar */
input[type="text"] {
  padding: 0.5rem;
  border: 1px solid var(--blue-whale-400);
  border-radius: 8px;
  margin-right: 1rem;
}

button {
  padding: 0.5rem 1rem;
  background-color: var(--blue-whale-500);
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: var(--blue-whale-700);
}
.category-card {
  width: 22rem;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  background-color: var(--blue-whale-100);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeInUp 0.6s ease both;
}

.category-card:hover {
  transform: scale(1.03);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.category-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-body {
  padding: 1rem;
}

.category-title {
  text-align: center;
  font-weight: 700;
  color: var(--blue-whale-900);
  margin-bottom: 0.5rem;
}

.category-question {
  color: var(--blue-whale-950);
  margin-bottom: 1rem;
}

.notification.unread {
  background-color: #f0f8ff;
}
.notification.read {
  background-color: #e0e0e0;
}


/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Animation pour les suggestions de recherche */
.suggestion-item {
  background-color: #f9f9f9;
  padding: 10px;
  margin: 5px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s ease-in-out, background-color 0.3s ease;
}

.suggestion-item:hover {
  background-color: #e2f1ff;
  transform: translateX(10px);
}

.suggestion-list {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
/* Style personnalisé pour le message d'alerte */
.custom-alert {
  background-color: #eaf6f6;
  border-left: 5px solid #00aff8;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  font-family: 'Arial', sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.custom-alert .alert-heading {
  font-size: 18px;
  color: #00796b;
}

.custom-alert p {
  color: #333;
}

.custom-alert .alert-button {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #00aff8;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.custom-alert .alert-button:hover {
  background-color: #00796b;
}
/* Section des catégories */
#categories {
  padding: 50px 0;
  background-color: #f4f8fb;
}

/* Cartes des catégories */
.category-card {
  background-color: #fff;
  border-radius: 15px;
  overflow: hidden;
  width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Image de la catégorie */
.category-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-img {
  transform: scale(1.1);
}

/* Corps de la carte */
.category-body {
  padding: 20px;
  text-align: center;
}

.category-title {
  font-size: 18px;
  color: #2c3e50;
  font-weight: bold;
}

.category-question {
  font-size: 14px;
  color: #7f8c8d;
  margin: 10px 0;
}

.category-card button {
  background-color: #00aff8;
  color: #fff;
  border: none;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.category-card button:hover {
  background-color: #00796b;
}

/* Animation de l'apparition des cartes */
.category-card {
  opacity: 0;
  animation: fadeIn 1s forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
#categories {
  background-color: #f9f9f9;
  padding: 50px 0;
}

.category-card {
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.category-card:hover {
  transform: scale(1.05);
}

.category-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
}

.category-img {
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.category-img:hover {
  transform: scale(1.1);
}

.btn {
  margin-top: 10px;
}

button.btn-secondary {
  font-size: 24px;
  padding: 10px;
}
.category-card {
  width: 250px;
  height: 300px;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.category-card:hover .category-img {
  transform: scale(1.1);
}

.category-img-container {
  position: relative;
  width: 100%;
  height: 70%;
  overflow: hidden;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-hover-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: none;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 10px;
}

.category-card:hover .category-hover-content {
  display: flex;
}

button.btn-primary {
  margin-top: 10px;
}
.category-box {
  border: 1px solid #74d8ff;
  border-radius: 12px;
  padding: 10px 20px;
  cursor: pointer;
  background-color: #eff9ff;
  transition: all 0.2s ease-in-out;
}

.category-box:hover {
  background-color: #b5e8ff;
  transform: scale(1.05);
}
.hover-bg:hover {
  background-color: #e7f7ff;
  border-radius: 5px;
}

.custom-alert {
  border-left: 5px solid #00aff8;
}
.search-card {
  background-color: #f9fbfc;
}

.search-card input::placeholder {
  color: #999;
}

.search-card .badge:hover {
  background-color: #d6f0ff;
  transition: 0.3s;
}
.navbar-nav .nav-link {
  font-weight: 500;
  font-size: 1.1rem;
  color: #054e75 !important;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.navbar-nav .nav-link:hover {
  background-color: #e6f3ff;
  color: #007bff !important;
}

.navbar-nav .nav-link.active {
  background-color: #def3ff;
  color: #006fac !important;
  font-weight: 600;
}
.navbar {
  background-color: #eff9ff !important;
  border-bottom: 2px solid #b5e8ff;
}

.navbar-brand {
  color: #032b44 !important; /* Bleu charbon : excellent contraste */
  font-weight: bold;
  font-size: 1.5rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  font-size: 1.1rem;
  color: #032b44 !important; /* Texte foncé lisible sur fond clair */
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.navbar-nav .nav-link:hover {
  background-color: #def3ff;
  color: #005e8e !important;
}

.navbar-nav .nav-link.active {
  background-color: #b5e8ff;
  color: #032b44 !important;
  font-weight: 600;
}
.footer-section {
  background-color: #eff9ff;
  color: #032b44;
  font-family: 'Segoe UI', sans-serif;
  padding-bottom: 20px;
}

.footer-title {
  font-weight: 700;
  color: #005e8e;
  margin-bottom: 1rem;
}

.footer-text {
  font-size: 1rem;
  color: #054e75;
}

.footer-links li a {
  color: #032b44;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links li a:hover {
  color: #00aff8;
  text-decoration: underline;
}

.footer-contact li {
  margin-bottom: 0.5rem;
  color: #032b44;
  font-size: 0.95rem;
}

.footer-bottom-text {
  color: #006fac;
  font-size: 0.9rem;
}

.newsletter-section {
  background-color: #def3ff;
  border-radius: 12px;
  padding: 30px;
  margin-top: 2rem;
}

.newsletter-input {
  width: 100%;
  max-width: 350px;
  padding: 10px 15px;
  border-radius: 10px;
  border: 1px solid #b5e8ff;
  transition: box-shadow 0.3s ease;
}

.newsletter-input:focus {
  outline: none;
  box-shadow: 0 0 8px #00aff8;
  border-color: #00aff8;
}

.newsletter-btn {
  background-color: #00aff8;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.newsletter-btn:hover {
  background-color: #008bd5;
}
/* Navbar Background Color */
.navbar {
  background-color: #eff9ff; /* Fond clair pour la navbar */
  padding: 1rem 2rem;
}

/* Couleur de texte des liens */
.nav-link {
  color: #032b44 !important; /* Texte des liens en bleu foncé */
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Couleur au survol */
.nav-link:hover {
  color: #00aff8 !important; /* Couleur au survol des liens */
}

/* Couleur du logo */
.navbar-brand {
  color: #008bd5 !important; /* Couleur du logo */
}

/* Active Link */
.navbar-nav .nav-item .nav-link.active {
  color: #00aff8 !important; /* Couleur active du lien */
  font-weight: bold; /* Gras pour le lien actif */
  border-bottom: 2px solid #00aff8; /* Ligne sous le lien actif */
}

/* Pour rendre la navbar visible sur le bouton de bascule (hamburger) */
.navbar-toggler {
  border-color: #00aff8;
}

/* Ajout de styles pour une meilleure visibilité sur les petits écrans */
@media (max-width: 991px) {
  .navbar-nav {
    text-align: center;
  }
}

.text-blue-whale {
  color: #006fac !important;
}

.bg-blue-whale {
  background-color: #006fac !important;
  color: white !important;
}

.border-blue-whale {
  border-color: #006fac !important;
}

.btn-blue-whale {
  background-color: #006fac;
  color: white;
  border: none;
}

.btn-blue-whale:hover {
  background-color: #005e8e;
}
/* Dans App.css */
.navbar-link-hover:hover {
  color: #005e8e !important;
  text-decoration: underline;
}

/* Ajout des styles créatifs */

/* Animated Background Shapes */
.animated-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 20s infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -150px;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -100px;
  animation-delay: -5s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: 50%;
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(50px, 50px) rotate(90deg); }
  50% { transform: translate(0, 100px) rotate(180deg); }
  75% { transform: translate(-50px, 50px) rotate(270deg); }
}

/* Floating Icons */
.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.2);
  animation: floatIcon 15s infinite;
}

.icon-1 { top: 20%; left: 10%; animation-delay: -2s; }
.icon-2 { top: 40%; right: 15%; animation-delay: -5s; }
.icon-3 { bottom: 30%; left: 20%; animation-delay: -8s; }

@keyframes floatIcon {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

/* Animated Title */
.animate-title {
  position: relative;
  display: inline-block;
}

.title-accent {
  color: var(--accent-color);
  margin-left: 10px;
  animation: pulse 2s infinite;
}

/* Search Input Animations */
.search-input-group.searching {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

.search-icon.rotate {
  animation: rotate360 0.5s ease;
}

@keyframes rotate360 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Suggestion Pills */
.suggestion-pill {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.suggestion-pill.hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.pill-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.suggestion-pill:hover .pill-icon {
  transform: rotate(15deg);
}

/* Feature Cards */
.feature-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.feature-card:hover::before {
  transform: translateX(100%);
}

.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--primary-gradient);
  z-index: -1;
  border-radius: 22px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
  opacity: 0.3;
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform .5s, opacity 1s;
}

.ripple:active::after {
  transform: scale(0, 0);
  opacity: .3;
  transition: 0s;
}

/* Pulse Animation */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Modal Animations */
.animate-modal {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .floating-icons {
    display: none;
  }

  .search-hero {
    padding: 1rem;
  }

  .feature-card {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .search-page {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .search-input {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .feature-card {
    background: rgba(255, 255, 255, 0.05);
  }
}
button.suggestion-item.ripple {
  color: black !important;
  background-color: #77c3ec;
  width: 100%;
  padding: 12px 20px;
  text-align: left;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

button.suggestion-item.ripple:hover {
  background-color: #005e8e;
  padding-left: 25px;
}

/* Styles pour l'indicateur de chargement */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.search-button.loading {
  opacity: 0.8;
  cursor: wait;
}

/* Style pour les messages d'erreur */
.search-error {
  background-color: #fff3f3;
  color: #ff4757;
  padding: 10px 15px;
  border-radius: 8px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  animation: fadeIn 0.3s ease-out;
}

.clear-error {
  background: none;
  border: none;
  color: #ff4757;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animation pour la cloche de notification */
@keyframes bellRing {
  0% { transform: rotate(0); }
  10% { transform: rotate(15deg); }
  20% { transform: rotate(-15deg); }
  30% { transform: rotate(10deg); }
  40% { transform: rotate(-10deg); }
  50% { transform: rotate(5deg); }
  60% { transform: rotate(-5deg); }
  70% { transform: rotate(0); }
  100% { transform: rotate(0); }
}

.bell-animation {
  animation: bellRing 1s ease-in-out;
  animation-iteration-count: 2;
}

/* Styles pour les notifications administrateur */
.notification-list {
  max-height: 600px;
  overflow-y: auto;
}

.notification-list .card {
  transition: all 0.3s ease;
}

.notification-list .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.notification-list .card-title {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.notification-list .btn-group {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.notification-list .card:hover .btn-group {
  opacity: 1;
}

/* Styles pour les différents types de notifications */
.notification-type-consultation {
  border-left: 4px solid #28a745 !important;
}

.notification-type-question {
  border-left: 4px solid #007bff !important;
}

.notification-type-contact {
  border-left: 4px solid #17a2b8 !important;
}

/* Styles pour le formulaire de demande de consultation */
.consultation-request-form {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 600px;
  margin: 0 auto;
}

.consultation-request-form .form-header {
  background: var(--primary-gradient);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.consultation-request-form .form-header h4 {
  margin: 0;
  font-weight: 600;
}

.consultation-request-form .close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.consultation-request-form form {
  padding: 20px;
}

.consultation-request-form .form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.consultation-request-form .form-group {
  flex: 1;
  margin-bottom: 15px;
}

.consultation-request-form label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  color: #006fac;
  font-weight: 500;
}

.consultation-request-form .icon {
  color: #006fac;
}

.consultation-request-form .form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.consultation-request-form .form-control:focus {
  border-color: #006fac;
  box-shadow: 0 0 0 3px rgba(0, 111, 172, 0.1);
  outline: none;
}

.consultation-request-form .form-actions {
  margin-top: 20px;
  text-align: center;
}

.consultation-request-form .btn-primary {
  background: var(--primary-gradient);
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.consultation-request-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 111, 172, 0.2);
}

.consultation-success {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.consultation-success .success-icon {
  width: 60px;
  height: 60px;
  background-color: #28a745;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin: 0 auto 20px;
}

.consultation-success h4 {
  color: #28a745;
  margin-bottom: 15px;
}

@media (max-width: 576px) {
  .consultation-request-form .form-row {
    flex-direction: column;
    gap: 0;
  }
}

/* Styles pour la cloche de notification */
.notification-bell-container {
  position: relative;
}

.notification-bell-button {
  background: none;
  border: none;
  color: #006fac;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.notification-bell-button:hover {
  background-color: rgba(0, 111, 172, 0.1);
}

/* Variante pour le dashboard */
.notification-bell-container.dashboard-variant {
  margin-left: auto;
}

.notification-bell-button.dashboard-button {
  background-color: white;
  border-radius: 8px;
  padding: 10px 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-bell-button.dashboard-button:hover {
  background-color: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.notification-text {
  font-size: 14px;
  font-weight: 500;
}

.notification-dropdown.dashboard-dropdown {
  right: 0;
  top: calc(100% + 10px);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ff4757;
  color: white;
  font-size: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  animation: fadeInDown 0.3s ease;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.notification-title {
  margin: 0;
  color: #006fac;
  font-weight: 600;
}

.view-all-link {
  color: #006fac;
  font-size: 12px;
  text-decoration: none;
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 15px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
}

.notification-item:hover {
  background-color: #f9f9f9;
}

.notification-item.unread {
  background-color: #f0f8ff;
}

.notification-content {
  flex: 1;
}

.notification-item-title {
  margin: 0 0 5px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.notification-text {
  margin: 0 0 5px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  color: #999;
  font-size: 11px;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background-color: #006fac;
  border-radius: 50%;
  margin-left: 10px;
  margin-top: 5px;
}

.notification-loading,
.notification-error,
.notification-empty {
  padding: 20px;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
