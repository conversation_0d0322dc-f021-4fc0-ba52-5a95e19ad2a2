import React, { useState, useEffect } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { HouseDoor, Bell, PersonCircle, Calendar3, Clock, QuestionCircle, BoxArrowRight } from 'react-bootstrap-icons';
import NotificationBell from '../../Components/NotificationBell';
import './DashboardMedecin.css';

const DashboardLayout = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const location = useLocation();
  const navigate = useNavigate();
  const [medecinNom, setMedecinNom] = useState('');

  // Vérifier si nous sommes sur la page des notifications
  const isNotificationsPage = location.pathname === '/medecin/notifications';

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);

    // Récupérer le nom du médecin depuis le localStorage
    const nom = localStorage.getItem('medecinNom');
    if (nom) {
      setMedecinNom(nom);
    }

    return () => clearInterval(timer);
  }, []);

  const handleLogout = () => {
    // Supprimer les informations d'authentification
    localStorage.removeItem('medecinToken');
    localStorage.removeItem('medecinNom');
    localStorage.removeItem('medecinId');

    // Rediriger vers la page de connexion
    navigate('/login-medecin');
  };

  // Vérifier si le lien est actif
  const isActive = (path) => {
    return location.pathname === `/medecin${path}`;
  };

  return (
    <div className="dashboard">
      <div className="sidebar">
        <div className="logo">
          <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
            <h4>OphtalmoAI</h4>
          </Link>
        </div>

        <div className="user-info">
          <PersonCircle size={24} />
          <span>Dr. {medecinNom || 'Médecin'}</span>
        </div>

        <ul className="nav-menu">
          <li className={`nav-item ${isActive('/dashboard') || isActive('') ? 'active' : ''}`}>
            <Link to="/medecin/dashboard">
              <HouseDoor /> Dashboard
            </Link>
          </li>
          <li className={`nav-item ${isActive('/questions') ? 'active' : ''}`}>
            <Link to="/medecin/questions">
              <QuestionCircle /> Questions
            </Link>
          </li>
          <li className={`nav-item ${isActive('/notifications') ? 'active' : ''}`}>
            <Link to="/medecin/notifications">
              <Bell /> Notifications
            </Link>
          </li>
          <li className={`nav-item ${isActive('/profil') ? 'active' : ''}`}>
            <Link to="/medecin/profil">
              <PersonCircle /> Profil
            </Link>
          </li>
        </ul>

        <div className="sidebar-footer">
          <div className="time-display">
            <Clock />
            <span>{currentTime.toLocaleTimeString()}</span>
          </div>

          <button className="logout-button" onClick={handleLogout}>
            <BoxArrowRight /> Déconnexion
          </button>
        </div>
      </div>

      <div className="main-content">
        {!isNotificationsPage && (
          <div className="dashboard-header">
            <div className="dashboard-title">
              <h2>Bienvenue, Dr. {medecinNom || 'Médecin'}</h2>
              <p>{new Date().toLocaleDateString('fr-FR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
            </div>
            <div className="dashboard-actions">
              <NotificationBell isAdmin={false} />
            </div>
          </div>
        )}
        <Outlet />
      </div>
    </div>
  );
};

export default DashboardLayout;