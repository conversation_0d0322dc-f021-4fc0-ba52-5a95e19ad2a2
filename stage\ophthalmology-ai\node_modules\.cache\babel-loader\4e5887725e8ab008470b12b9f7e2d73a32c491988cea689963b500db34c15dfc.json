{"ast": null, "code": "import axios from \"axios\";\nimport store from './store/store'; // Assure-toi que le chemin est correct\n\nconst API = axios.create({\n  baseURL: \"http://localhost:8000/api/\"\n});\n\n// Vérifier si le token est défini avant de l'ajouter aux requêtes\nAPI.interceptors.request.use(config => {\n  const state = store.getState();\n\n  // Vérifier d'abord le token médecin dans localStorage\n  const medecinToken = localStorage.getItem('medecinToken');\n  if (medecinToken) {\n    config.headers.Authorization = `Bearer ${medecinToken}`;\n  } else if (state.user && state.user.token) {\n    config.headers.Authorization = `Bearer ${state.user.token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nexport default API;", "map": {"version": 3, "names": ["axios", "store", "API", "create", "baseURL", "interceptors", "request", "use", "config", "state", "getState", "medecinToken", "localStorage", "getItem", "headers", "Authorization", "user", "token", "error", "Promise", "reject"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/stage/ophthalmology-ai/src/api.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport store from './store/store'; // Assure-toi que le chemin est correct\r\n\r\nconst API = axios.create({\r\n  baseURL: \"http://localhost:8000/api/\",\r\n});\r\n\r\n// Vérifier si le token est défini avant de l'ajouter aux requêtes\r\nAPI.interceptors.request.use((config) => {\r\n  const state = store.getState();\r\n\r\n  // Vérifier d'abord le token médecin dans localStorage\r\n  const medecinToken = localStorage.getItem('medecinToken');\r\n\r\n  if (medecinToken) {\r\n    config.headers.Authorization = `Bearer ${medecinToken}`;\r\n  } else if (state.user && state.user.token) {\r\n    config.headers.Authorization = `Bearer ${state.user.token}`;\r\n  }\r\n\r\n  return config;\r\n}, (error) => {\r\n  return Promise.reject(error);\r\n});\r\n\r\nexport default API;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,eAAe,CAAC,CAAC;;AAEnC,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAF,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGR,KAAK,CAACS,QAAQ,CAAC,CAAC;;EAE9B;EACA,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAEzD,IAAIF,YAAY,EAAE;IAChBH,MAAM,CAACM,OAAO,CAACC,aAAa,GAAG,UAAUJ,YAAY,EAAE;EACzD,CAAC,MAAM,IAAIF,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACO,IAAI,CAACC,KAAK,EAAE;IACzCT,MAAM,CAACM,OAAO,CAACC,aAAa,GAAG,UAAUN,KAAK,CAACO,IAAI,CAACC,KAAK,EAAE;EAC7D;EAEA,OAAOT,MAAM;AACf,CAAC,EAAGU,KAAK,IAAK;EACZ,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CAAC;AAEF,eAAehB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}