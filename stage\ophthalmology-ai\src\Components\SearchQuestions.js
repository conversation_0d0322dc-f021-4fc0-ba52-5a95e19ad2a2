// // src/components/SearchQuestions.js
// import React, { useState } from 'react';
// import { useDispatch } from 'react-redux';
// import { getResponse } from '../actions/actions';

// const SearchQuestions = () => {
//   const [questionText, setQuestionText] = useState('');
//   const dispatch = useDispatch();

//   const handleSearch = (e) => {
//     e.preventDefault();
//     dispatch(getResponse(questionText));  // Envoie la question au backend
//   };

//   return (
//     <div>
//       <form onSubmit={handleSearch}>
//         <input
//           type="text"
//           placeholder="Posez votre question"
//           value={questionText}
//           onChange={(e) => setQuestionText(e.target.value)}
//         />
//         <button type="submit">Rechercher</button>
//       </form>
//     </div>
//   );
// };

// export default SearchQuestions;
