// import axios from 'axios';

// const handleLogin = async () => {
//   try {
//     const response = await axios.post('http://localhost:8000/api/login', {
//       email: email, // depuis ton formulaire
//       password: password,
//     });

//     const token = response.data.token;
//     localStorage.setItem('medecinToken', token);

//     // Rediriger vers le tableau de bord médecin
//   } catch (error) {
//     alert("Erreur de connexion");
//   }
// };
