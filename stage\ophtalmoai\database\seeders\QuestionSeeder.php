<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Question;
use App\Models\Category;

class QuestionSeeder extends Seeder {
    public function run() {
        $patientId = 1;
        $categories = Category::all()->keyBy('name');

        $questions = [
            // <PERSON><PERSON><PERSON>
            ['fr' => 'Je vois des points noirs, est-ce grave ?',
             'en' => 'I see black spots, is it serious?',
             'darija' => 'Kanchoof nokat k7al, wach khatira?',
             'category' => 'R<PERSON>tine',
             'necessite' => true],

            // Cataracte
            ['fr' => 'Ma vision est trouble comme à travers un voile',
             'en' => 'My vision is blurry, like through a veil',
             'darija' => 'Roya diali machya k7al, b7al chi sfenja',
             'category' => 'Cataracte',
             'necessite' => true],

            // Glaucome
            ['fr' => 'Est-ce que le glaucome est douloureux ?',
             'en' => 'Is glaucoma painful?',
             'darija' => 'Wach lglaucome kaydour?',
             'category' => 'Glaucome',
             'necessite' => true],

            // Autres
            ['fr' => 'Est-ce dangereux de rester longtemps devant un écran ?',
             'en' => 'Is it dangerous to stare at a screen too long?',
             'darija' => 'Wach khatar nb9a 9dam l’ecran bzzaf?',
             'category' => 'Autres',
             'necessite' => false],
        ];

        foreach ($questions as $q) {
            foreach (['fr', 'en', 'darija'] as $lang) {
                Question::create([
                    'texte' => $q[$lang],
                    'patient_id' => $patientId,
                    'category_id' => $categories[$q['category']]->id,
                    'langue' => $lang,
                    'necessite_consultation' => $q['necessite'],
                ]);
            }
        }
    }
}
