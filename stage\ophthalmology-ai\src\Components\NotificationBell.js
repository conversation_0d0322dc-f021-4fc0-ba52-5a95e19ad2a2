import React, { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import axios from 'axios';
import { Link, useLocation } from 'react-router-dom';

const NotificationBell = ({ isAdmin = false, variant = "default" }) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dropdownRef = useRef(null);
  const location = useLocation();

  // Déterminer si nous sommes dans le dashboard ou dans la navbar
  const isDashboard = location.pathname.startsWith('/medecin') || location.pathname.startsWith('/admin');

  const fetchNotifications = async () => {
    try {
      setLoading(true);

      // Déterminer l'URL en fonction du type d'utilisateur
      const url = isAdmin
        ? 'http://localhost:8000/api/admin/notifications/recent'
        : 'http://localhost:8000/api/medecins/notifications/recent';

      // Récupérer l'ID et le token appropriés
      const id = isAdmin
        ? localStorage.getItem('adminId') || '1'
        : localStorage.getItem('medecinId') || '1';

      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('medecinToken');

      // En développement, utiliser des données fictives si l'API échoue
      try {
        const response = await axios.get(url, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });

        const notifs = response.data.slice(0, 5); // Limiter à 5 notifications
        setNotifications(notifs);
        setUnreadCount(notifs.filter(n => !n.lu).length);
      } catch (apiError) {
        console.error('Erreur API:', apiError);

        // En développement, utiliser les données du localStorage ou des données fictives
        if (process.env.NODE_ENV !== 'production') {
          // Vérifier s'il y a des notifications simulées dans le localStorage
          const storedNotifs = localStorage.getItem('mockNotifications');
          let mockNotifications;

          if (storedNotifs) {
            // Utiliser les notifications stockées dans le localStorage
            mockNotifications = JSON.parse(storedNotifs);
            console.log("Notifications récupérées du localStorage:", mockNotifications);
          } else {
            // Utiliser des données fictives par défaut
            mockNotifications = [
              {
                id: 1,
                titre: isAdmin ? 'Nouvelle demande de consultation' : 'Nouveau message',
                contenu: isAdmin
                  ? 'Patient: Jean Dupont - Motif: Douleur à l\'œil gauche'
                  : 'Vous avez un nouveau message',
                lu: false,
                created_at: new Date().toISOString(),
                type: 'consultation'
              },
              {
                id: 2,
                titre: 'Rappel',
                contenu: 'Vous avez une tâche en attente',
                lu: true,
                created_at: new Date(Date.now() - 86400000).toISOString(),
                type: 'rappel'
              }
            ];

            // Stocker ces notifications par défaut dans le localStorage
            localStorage.setItem('mockNotifications', JSON.stringify(mockNotifications));
          }

          setNotifications(mockNotifications);
          setUnreadCount(mockNotifications.filter(n => !n.lu).length);
        } else {
          throw apiError;
        }
      }

      setError(null);
    } catch (error) {
      console.error('Erreur lors de la récupération des notifications:', error);
      setError('Erreur lors de la récupération des notifications');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      // Essayer d'abord d'appeler l'API
      try {
        const url = isAdmin
          ? `http://localhost:8000/api/admin/notifications/${notificationId}/mark-read`
          : `http://localhost:8000/api/notifications/${notificationId}/mark-read`;

        const token = isAdmin
          ? localStorage.getItem('adminToken')
          : localStorage.getItem('medecinToken');

        await axios.patch(url, {}, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors du marquage comme lu:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif =>
              notif.id === notificationId ? { ...notif, lu: true } : notif
            );

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Notification marquée comme lue dans le localStorage");
          }
        } else {
          throw apiError; // En production, propager l'erreur
        }
      }

      // Mettre à jour l'état local
      setNotifications(notifications.map(notif =>
        notif.id === notificationId ? { ...notif, lu: true } : notif
      ));

      // Mettre à jour le compteur
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
    }
  };

  // Fermer le dropdown quand on clique en dehors
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Récupérer les notifications au chargement et toutes les minutes
  useEffect(() => {
    fetchNotifications();

    const interval = setInterval(fetchNotifications, 60000);
    return () => clearInterval(interval);
  }, [isAdmin]);

  return (
    <div className={`notification-bell-container ${isDashboard ? 'dashboard-variant' : ''}`} ref={dropdownRef}>
      <button
        className={`notification-bell-button ${isDashboard ? 'dashboard-button' : ''}`}
        onClick={() => setShowDropdown(!showDropdown)}
        aria-label="Notifications"
      >
        <Bell size={isDashboard ? 24 : 20} className={unreadCount > 0 ? "bell-animation" : ""} />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
        {isDashboard && <span className="notification-text">Notifications</span>}
      </button>

      {showDropdown && (
        <div className={`notification-dropdown ${isDashboard ? 'dashboard-dropdown' : ''}`}>
          <div className="notification-header">
            <h6 className="notification-title">Notifications</h6>
            <Link
              to={isAdmin ? "/admin/notifications" : "/medecin/notifications"}
              className="view-all-link"
              onClick={() => setShowDropdown(false)}
              state={{ fromBell: true }}
            >
              Voir tout
            </Link>
          </div>

          <div className="notification-list">
            {loading ? (
              <div className="notification-loading">
                <div className="spinner-border spinner-border-sm text-primary" role="status">
                  <span className="visually-hidden">Chargement...</span>
                </div>
                <span>Chargement...</span>
              </div>
            ) : error ? (
              <div className="notification-error">{error}</div>
            ) : notifications.length === 0 ? (
              <div className="notification-empty">
                <Bell size={20} className="text-muted" />
                <p>Aucune notification</p>
              </div>
            ) : (
              notifications.map(notif => (
                <div
                  key={notif.id}
                  className={`notification-item ${!notif.lu ? 'unread' : ''}`}
                  onClick={() => markAsRead(notif.id)}
                >
                  <div className="notification-content">
                    <h6 className="notification-item-title">{notif.titre}</h6>
                    <p className="notification-text">{notif.contenu}</p>
                    <small className="notification-time">
                      {new Date(notif.created_at).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </small>
                  </div>
                  {!notif.lu && <div className="unread-indicator"></div>}
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
