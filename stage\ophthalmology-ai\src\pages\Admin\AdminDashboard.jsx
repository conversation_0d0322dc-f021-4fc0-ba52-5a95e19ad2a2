// import React from "react";
// import { Link, Outlet, useNavigate } from "react-router-dom";

// const AdminDashboard = () => {
//   const navigate = useNavigate();

//   const handleLogout = () => {
//     localStorage.removeItem("adminToken");
//     navigate("/admin/login");
//   };

//   return (
//     <div className="flex h-screen">
//       {/* Sidebar */}
//       <div className="w-64 bg-blue-900 text-white p-5 space-y-4">
//         <h2 className="text-2xl font-bold mb-6">Admin OphtalmoAI</h2>
//         <Link to="/admin/messages" className="block hover:underline">📬 Messages</Link>
//         <Link to="/admin/notifications" className="block hover:underline">🔔 Notifications</Link>
//         <button onClick={handleLogout} className="mt-10 text-red-300 hover:text-red-500">
//           🚪 Déconnexion
//         </button>
//       </div>

//       {/* Contenu */}
//       <div className="flex-1 p-6 bg-gray-50 overflow-y-auto">
//         <Outlet />
//       </div>
//     </div>
//   );
// };

// export default AdminDashboard;
