import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Bell, Person, Calendar, Check, Trash } from 'react-bootstrap-icons';

const AdminNotificationsPanel = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasNewNotifications, setHasNewNotifications] = useState(false);

  const fetchNotifications = async () => {
    try {
      setLoading(true);

      // Utiliser des données de test si nous sommes en développement et que l'ID de l'admin n'est pas disponible
      const adminId = localStorage.getItem('adminId') || '1'; // ID par défaut pour le développement
      const token = localStorage.getItem('adminToken') || localStorage.getItem('medecinToken'); // Utiliser le token médecin si disponible

      // En environnement de production, nous voulons toujours vérifier l'ID
      if (process.env.NODE_ENV === 'production' && !localStorage.getItem('adminId') && !localStorage.getItem('medecinId')) {
        setError('Identifiant de l\'administrateur non trouvé. Veuillez vous reconnecter.');
        setLoading(false);
        return;
      }

      try {
        // Essayer d'abord de récupérer les notifications depuis l'API
        const response = await axios.get(`http://localhost:8000/api/admin/notifications`, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });

        const newNotifications = response.data;
        setNotifications(newNotifications);

        // Vérifier s'il y a de nouvelles notifications non lues
        const unreadCount = newNotifications.filter(n => !n.lu).length;
        setHasNewNotifications(unreadCount > 0);

        setError(null);
      } catch (apiError) {
        console.error('Erreur API:', apiError);

        // Si l'API échoue, utiliser les données du localStorage ou des données de test en développement
        if (process.env.NODE_ENV !== 'production') {
          // Vérifier s'il y a des notifications simulées dans le localStorage
          const storedNotifs = localStorage.getItem('mockNotifications');
          let mockNotifications;

          if (storedNotifs) {
            // Utiliser les notifications stockées dans le localStorage
            mockNotifications = JSON.parse(storedNotifs);
            console.log("Notifications récupérées du localStorage:", mockNotifications);
          } else {
            // Utiliser des données fictives par défaut
            mockNotifications = [
              {
                id: 1,
                titre: 'Nouvelle demande de consultation',
                contenu: 'Patient: Jean Dupont - Motif: Douleur à l\'œil gauche - Téléphone: **********',
                type: 'consultation',
                patient_id: 123,
                patient_nom: 'Jean Dupont',
                lu: false,
                created_at: new Date().toISOString()
              },
              {
                id: 2,
                titre: 'Question urgente',
                contenu: 'Patient: Marie Martin - Question: J\'ai une rougeur soudaine dans l\'œil droit, est-ce grave?',
                type: 'question',
                patient_id: 124,
                patient_nom: 'Marie Martin',
                lu: true,
                created_at: new Date(Date.now() - 86400000).toISOString() // Hier
              },
              {
                id: 3,
                titre: 'Nouveau message de contact',
                contenu: 'Email: <EMAIL> - Message: Je souhaite prendre rendez-vous pour un examen de la vue.',
                type: 'contact',
                lu: false,
                created_at: new Date(Date.now() - 172800000).toISOString() // Avant-hier
              }
            ];

            // Stocker ces notifications par défaut dans le localStorage
            localStorage.setItem('mockNotifications', JSON.stringify(mockNotifications));
          }

          setNotifications(mockNotifications);
          setHasNewNotifications(mockNotifications.filter(n => !n.lu).length > 0);
          setError(null);
        } else {
          throw apiError; // En production, propager l'erreur
        }
      }
    } catch (error) {
      setError('Erreur lors de la récupération des notifications');
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('medecinToken');

      try {
        // Essayer d'abord d'appeler l'API
        await axios.patch(`http://localhost:8000/api/admin/notifications/${notificationId}/mark-read`, {}, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors du marquage comme lu:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif =>
              notif.id === notificationId ? { ...notif, lu: true } : notif
            );

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Notification marquée comme lue dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Mettre à jour l'état local même si l'API échoue (en développement)
      setNotifications(notifications.map(notif =>
        notif.id === notificationId ? { ...notif, lu: true } : notif
      ));

      // Mettre à jour le statut des nouvelles notifications
      const remainingUnread = notifications.filter(n => !n.lu && n.id !== notificationId).length;
      setHasNewNotifications(remainingUnread > 0);
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
      setError('Erreur lors du marquage de la notification comme lue');
    }
  };

  const markAllAsRead = async () => {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('medecinToken');

      try {
        // Essayer d'abord d'appeler l'API
        await axios.patch(`http://localhost:8000/api/admin/notifications/mark-all-read`, {}, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors du marquage de toutes les notifications:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif => ({ ...notif, lu: true }));

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Toutes les notifications marquées comme lues dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Mettre à jour l'état local même si l'API échoue (en développement)
      setNotifications(notifications.map(notif => ({ ...notif, lu: true })));
      setHasNewNotifications(false);
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les notifications:', error);
      setError('Erreur lors du marquage des notifications comme lues');
    }
  };

  const deleteNotification = async (notificationId) => {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('medecinToken');

      try {
        // Essayer d'abord d'appeler l'API
        await axios.delete(`http://localhost:8000/api/admin/notifications/${notificationId}`, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors de la suppression de la notification:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.filter(notif => notif.id !== notificationId);

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log("Notification supprimée dans le localStorage");
          }
        } else {
          // En production, propager l'erreur
          throw apiError;
        }
      }

      // Supprimer la notification de l'état local même si l'API échoue (en développement)
      setNotifications(notifications.filter(notif => notif.id !== notificationId));

      // Mettre à jour le statut des nouvelles notifications
      const remainingUnread = notifications.filter(n => !n.lu && n.id !== notificationId).length;
      setHasNewNotifications(remainingUnread > 0);
    } catch (error) {
      console.error('Erreur lors de la suppression de la notification:', error);
      setError('Erreur lors de la suppression de la notification');
    }
  };

  // Fonction pour répondre à une demande de consultation
  const handleConsultation = async (notificationId, patientId, accept = true) => {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('medecinToken');

      try {
        // Appeler l'API pour accepter ou refuser la consultation
        await axios.post(`http://localhost:8000/api/admin/consultations/${patientId}/${accept ? 'accept' : 'reject'}`, {
          notification_id: notificationId
        }, {
          headers: token ? { Authorization: `Bearer ${token}` } : {}
        });
      } catch (apiError) {
        console.error('Erreur API lors de la gestion de la consultation:', apiError);

        // En développement, mettre à jour les notifications simulées dans le localStorage
        if (process.env.NODE_ENV !== 'production') {
          const storedNotifs = localStorage.getItem('mockNotifications');

          if (storedNotifs) {
            const mockNotifications = JSON.parse(storedNotifs);
            const updatedNotifications = mockNotifications.map(notif =>
              notif.id === notificationId
                ? {
                    ...notif,
                    lu: true,
                    contenu: notif.contenu + (accept ? ' - Acceptée' : ' - Refusée')
                  }
                : notif
            );

            localStorage.setItem('mockNotifications', JSON.stringify(updatedNotifications));
            console.log(`Consultation ${accept ? 'acceptée' : 'refusée'} dans le localStorage`);
          }
        } else {
          throw apiError;
        }
      }

      // Marquer la notification comme lue
      await markAsRead(notificationId);

      // Mettre à jour l'interface avec un message de succès
      setNotifications(notifications.map(notif =>
        notif.id === notificationId
          ? {
              ...notif,
              lu: true,
              contenu: notif.contenu + (accept ? ' - Acceptée' : ' - Refusée')
            }
          : notif
      ));

    } catch (error) {
      console.error('Erreur lors de la gestion de la consultation:', error);
      setError('Erreur lors de la gestion de la demande de consultation');
    }
  };

  useEffect(() => {
    fetchNotifications();

    // Rafraîchir les notifications toutes les 60 secondes
    const interval = setInterval(fetchNotifications, 60000);

    // Nettoyage à la désinscription du composant
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="text-center p-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Chargement...</span>
        </div>
        <p className="mt-3">Chargement des notifications...</p>
      </div>
    );
  }

  const unreadCount = notifications.filter(n => !n.lu).length;

  return (
    <div className="container py-4">
      <div className="card shadow-sm">
        <div className="card-header bg-white d-flex justify-content-between align-items-center">
          <h3 className="mb-0 d-flex align-items-center">
            <Bell className={`me-2 ${hasNewNotifications ? "bell-animation" : ""}`} />
            Notifications des patients
            {unreadCount > 0 && (
              <span className="badge bg-danger ms-2">{unreadCount}</span>
            )}
          </h3>
          <div>
            <button
              onClick={fetchNotifications}
              className="btn btn-outline-primary btn-sm me-2"
              title="Rafraîchir les notifications"
            >
              ↻ Rafraîchir
            </button>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="btn btn-outline-secondary btn-sm"
                title="Tout marquer comme lu"
              >
                Tout marquer comme lu
              </button>
            )}
          </div>
        </div>

        <div className="card-body">
          {error && (
            <div className="alert alert-danger alert-dismissible fade show" role="alert">
              {error}
              <button type="button" className="btn-close" onClick={() => setError(null)}></button>
            </div>
          )}

          {notifications.length === 0 ? (
            <div className="text-center py-5">
              <Bell size={40} className="text-muted mb-3" />
              <p className="text-muted">Aucune notification</p>
            </div>
          ) : (
            <div className="notification-list">
              {notifications.map((notif) => (
                <div
                  key={notif.id}
                  className={`card mb-3 border-start ${notif.lu ? 'border-secondary' : 'border-primary'}`}
                  style={{ borderLeftWidth: '4px' }}
                >
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-start">
                      <h5 className="card-title">
                        {getNotificationIcon(notif.type)}
                        {notif.titre}
                        {!notif.lu && <span className="badge bg-primary ms-2">Nouveau</span>}
                      </h5>
                      <div className="btn-group">
                        {!notif.lu && (
                          <button
                            onClick={() => markAsRead(notif.id)}
                            className="btn btn-sm btn-outline-primary"
                            title="Marquer comme lu"
                          >
                            <Check />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notif.id)}
                          className="btn btn-sm btn-outline-danger"
                          title="Supprimer"
                        >
                          <Trash />
                        </button>
                      </div>
                    </div>
                    <p className="card-text">{notif.contenu}</p>

                    {/* Actions spécifiques selon le type de notification */}
                    {notif.type === 'consultation' && !notif.lu && (
                      <div className="mt-3">
                        <button
                          className="btn btn-success btn-sm me-2"
                          onClick={() => handleConsultation(notif.id, notif.patient_id, true)}
                        >
                          Accepter la demande
                        </button>
                        <button
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => handleConsultation(notif.id, notif.patient_id, false)}
                        >
                          Refuser
                        </button>
                      </div>
                    )}

                    <small className="text-muted d-block mt-2">
                      {new Date(notif.created_at).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </small>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Fonction utilitaire pour obtenir l'icône selon le type de notification
const getNotificationIcon = (type) => {
  switch (type) {
    case 'consultation':
      return <Calendar className="me-2 text-success" />;
    case 'question':
      return <Bell className="me-2 text-primary" />;
    case 'contact':
      return <Person className="me-2 text-info" />;
    default:
      return <Bell className="me-2 text-secondary" />;
  }
};

export default AdminNotificationsPanel;
