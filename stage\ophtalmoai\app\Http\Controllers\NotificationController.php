<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function index($medecinId)
    {
        return Notification::where('medecin_id', $medecinId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function markAsRead($id)
    {
        $notification = Notification::findOrFail($id);
        $notification->update(['lu' => true]);
        return response()->json(['message' => 'Notification marquée comme lue']);
    }

    public function markAllAsRead($medecinId)
    {
        Notification::where('medecin_id', $medecinId)
            ->where('lu', false)
            ->update(['lu' => true]);
        
        return response()->json(['message' => 'Toutes les notifications ont été marquées comme lues']);
    }
}