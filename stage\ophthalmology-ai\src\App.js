import { Route, Routes, Link } from "react-router-dom";
import SearchBar2 from "./Components/SearchBar2";
import AboutPage from "./pages/AboutPage";
import ContactPage from "./pages/ContactPage";
import CategoriesPage from "./pages/CategoriesPage";
import NotFound from "./pages/NotFound";
import Footer from "./Components/Footer";
import ProfilMedecin from "./pages/Admin/ProfilMedecin";
import NotificationsPanel from "./pages/Admin/NotificationsPanel";
import AdminNotificationsPanel from "./pages/Admin/AdminNotificationsPanel";

//new

import MedecinLogin from "./pages/Admin/MedecinLogin";
import DashboardMedecin from "./pages/Admin/DashboardMedecin";
import PrivateRoute from "./Components/PrivateRoute";
import MedecinRegister from './pages/Admin/MedecinRegister';
import DashboardLayout from './pages/Admin/DashboardLayout';

import "./style.css";
import QuestionsManagement from "./pages/Admin/QuestionsManagement";
import AddQuestion from "./pages/Admin/AddQuestion";
import EditQuestion from "./pages/Admin/EditQuestion";
import Navbar from "./Components/Navbar";

const App = () => {
  return (
    <div className="app-container">
      <Navbar />
      <Routes>
        <Route path="/" element={<SearchBar2 />} />
        <Route path="/a-propos" element={<AboutPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/categories" element={<CategoriesPage />} />
        <Route path="*" element={<NotFound />} />

        {/* Routes administrateur */}
        <Route path="/admin/notifications" element={<AdminNotificationsPanel />} />
        {/* <Route path="/admin/messages" element={<AdminMessages />} />
        <Route path="/admin/login" element={<AdminLogin />} /> */}

        <Route path="/login-medecin" element={<MedecinLogin />} />
        <Route path="/register-medecin" element={<MedecinRegister />} />

        {/* Routes protégées du dashboard médecin */}
        <Route path="/medecin" element={
          <PrivateRoute>
            <DashboardLayout />
          </PrivateRoute>
        }>
          <Route index element={<DashboardMedecin />} />
          <Route path="dashboard" element={<DashboardMedecin />} />
          <Route path="profil" element={<ProfilMedecin />} />
          <Route path="notifications" element={<NotificationsPanel />} />
          <Route path="questions" element={<QuestionsManagement />} />
          <Route path="questions/ajouter" element={<AddQuestion />} />
          <Route path="questions/modifier/:id" element={<EditQuestion />} />
        </Route>
      </Routes>
      <Footer />
    </div>
  );
};

export default App;

