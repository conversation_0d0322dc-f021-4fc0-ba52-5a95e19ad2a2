<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin; 
use App\Models\Notification;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function login(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'password' => 'required|string'
        ]);

        $admin = Admin::where('email', $validated['email'])->first();



        if (!$admin || !Hash::check($validated['password'], $admin->password)) {
            return response()->json(['error' => 'Identifiants incorrects'], 401);
        }
        

        return response()->json([
            'token' => 'fake-admin-token',
            'admin' => $admin
        ]);
    }

    public function getNotifications()
    {
        $notifications = Notification::latest()->get();
        return response()->json($notifications);
    }
}
