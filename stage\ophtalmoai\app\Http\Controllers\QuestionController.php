<?php

namespace App\Http\Controllers;

use App\Models\Medecin;
use App\Models\Question;
use App\Models\Reponse;
use App\Models\RendezVous;
use App\Models\Notification;
use App\Notifications\NouvelleQuestionNotification;
use Illuminate\Http\Request;

class QuestionController extends Controller 
{
    /**
     * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les questions avec les relations associées.
     */
    public function index() 
{ 
    $questions = Question::with(['patient', 'category', 'reponses'])
        ->select('questions.*') // Sélectionner explicitement les colonnes
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($question) {
            return [
                'id' => $question->id,
                'texte' => $question->texte,
                'reponse' => $question->reponses->first() ? $question->reponses->first()->texte : null,
                'categorie' => [
                    'nom' => $question->category ? $question->category->name : null
                ],
                'created_at' => $question->created_at
            ];
        });

    return response()->json($questions);
}

    /**
     * Afficher une question spécifique avec ses réponses.
     */
    public function show($id)
    {
        $question = Question::with(['patient', 'category', 'reponses'])
            ->findOrFail($id);

        return response()->json([
            'id' => $question->id,
            'texte' => $question->texte,
            'langue' => $question->langue,
            'necessite_consultation' => $question->necessite_consultation,
            'patient' => $question->patient ? [
                'id' => $question->patient->id,
                'nom' => $question->patient->nom,
                'email' => $question->patient->email
            ] : null,
            'categorie' => $question->category ? [
                'id' => $question->category->id,
                'nom' => $question->category->name
            ] : null,
            'reponses' => $question->reponses->map(function ($reponse) {
                return [
                    'id' => $reponse->id,
                    'texte' => $reponse->texte,
                    'audio_url' => $reponse->audio_url,
                    'mots_cles' => $reponse->mots_cles,
                    'medecin' => $reponse->medecin ? [
                        'id' => $reponse->medecin->id,
                        'nom' => $reponse->medecin->nom
                    ] : null,
                    'created_at' => $reponse->created_at
                ];
            }),
            'created_at' => $question->created_at,
            'updated_at' => $question->updated_at
        ]);
    }

public function getQuestionsGestion()
{
    $questions = Question::with(['category', 'reponses'])
        ->select('questions.*')
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($question) {
            return [
                'id' => $question->id,
                'texte' => $question->texte,
                'reponse' => [
                    'texte' => $question->reponses->first() ? $question->reponses->first()->texte : null
                ],
                'categorie' => [
                    'nom' => $question->category ? $question->category->name : null
                ],
                'created_at' => $question->created_at
            ];
        });

    return response()->json($questions);
}

    /**
     * Récupérer les questions d'un patient spécifique.
     */
    public function getPatientQuestions(Request $request)
    {
        // Récupérer l'utilisateur connecté (doit être un patient)
        $patient = $request->user();

        if (!$patient) {
            return response()->json([
                'error' => 'Patient non trouvé'
            ], 404);
        }

        $questions = Question::with(['category', 'reponses'])
            ->where('patient_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($question) {
                return [
                    'id' => $question->id,
                    'texte' => $question->texte,
                    'langue' => $question->langue,
                    'necessite_consultation' => $question->necessite_consultation,
                    'categorie' => $question->category ? [
                        'id' => $question->category->id,
                        'nom' => $question->category->name
                    ] : null,
                    'reponses' => $question->reponses->map(function ($reponse) {
                        return [
                            'id' => $reponse->id,
                            'texte' => $reponse->texte,
                            'audio_url' => $reponse->audio_url,
                            'medecin' => $reponse->medecin ? [
                                'nom' => $reponse->medecin->nom
                            ] : null,
                            'created_at' => $reponse->created_at
                        ];
                    }),
                    'created_at' => $question->created_at
                ];
            });

        return response()->json([
            'patient' => [
                'id' => $patient->id,
                'nom' => $patient->nom,
                'email' => $patient->email
            ],
            'questions' => $questions,
            'total' => $questions->count()
        ]);
    }

    /**
     * Ajouter une nouvelle question.
     */
    public function store(Request $request)
    {
        // Validation des données reçues
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string',
            'medecin_id' => 'required|exists:medecins,id', // S'assurer que le médecin existe
        ]);

        // Création de la question
        $question = Question::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'message' => $validated['message'],
            'medecin_id' => $validated['medecin_id'],
        ]);

        // Envoyer une notification au médecin
        Notification::create([
            'user_id' => $validated['medecin_id'],
            'message' => "Vous avez une nouvelle question de : " . $validated['name'],
        ]);

        return response()->json([
            'message' => 'Votre demande a été envoyée avec succès.',
            'question' => $question,
        ], 201);
    }


    /**
     * Récupérer la réponse à une question.
     */
    public function getReponse(Request $request)
    {
        $texte = strtolower($request->input('question'));
    
        $question = Question::with('reponses', 'category')
            ->whereRaw('LOWER(texte) LIKE ?', ["%{$texte}%"])
            ->first();
    
        if (!$question || $question->reponses->isEmpty()) {
            return response()->json([
                'message' => "Nous n'avons pas trouvé de réponse à votre question. Veuillez consulter un médecin.",
                'necessite_consultation' => true,
                'specialite' => $question && $question->category ? $question->category->name : 'Généraliste'
            ]);
        }
    
        return response()->json([
            'message' => $question->reponses->first()->texte,
            'necessite_consultation' => $question->necessite_consultation ?? false
        ]);
    }
    
    public function update(Request $request, $id)
{
    $question = Question::findOrFail($id);

    $request->validate([
        'texte' => 'required|string',
        'category_id' => 'nullable|exists:categories,id',
    ]);

    $question->update([
        'texte' => $request->texte,
        'category_id' => $request->category_id,
        'necessite_consultation' => $request->necessite_consultation ?? false,
    ]);

    return response()->json($question); // Retourne la question mise à jour
}
public function destroy($id)
{
    $question = Question::findOrFail($id);
    $question->delete();

    return response()->json(['message' => 'Question supprimée avec succès.']);
}


    /**
     * Rechercher une question.
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        
        $question = Question::where('texte', 'LIKE', "%{$query}%")
            ->with('reponses')
            ->first();

        if (!$question || $question->reponses->isEmpty()) {
            return response()->json([
                'message' => "Aucune réponse disponible. Nous vous conseillons de consulter un médecin.",
                'necessite_consultation' => true
            ]);
        }

        return response()->json([
            'reponse' => $question->reponses->first()->texte,
            'audio_url' => $question->reponses->first()->audio_url ?? null,
            'necessite_consultation' => $question->necessite_consultation
        ]);
    }

    /**
     * Demande de consultation.
     */
    public function demandeConsultation(Request $request)
    {
        $validatedData = $request->validate([
            'patient_id' => 'required|exists:patients,id',
            'medecin_id' => 'required|exists:medecins,id',
            'motif' => 'required|string',
            'date_heure' => 'required|date'
        ]);

        $rendezVous = RendezVous::create($validatedData);

        return response()->json([
            'message' => 'Votre demande de consultation a été envoyée avec succès.',
            'rendez_vous' => $rendezVous
        ], 201);
    }
    public function storeFromAdmin(Request $request)
{
    $validated = $request->validate([
        'texte' => 'required|string',
        'langue' => 'required|in:fr,en,darija',
        'category_id' => 'nullable|exists:categories,id',
        'patient_id' => 'nullable|exists:patients,id',
        'reponse' => 'required|string'
    ]);

    $question = Question::create($validated);

    $reponse = Reponse::create([
        'texte' => $validated['reponse'],
        'question_id' => $question->id,
    ]);

    return response()->json([
        'message' => 'Question ajoutée avec succès.',
        'question' => $question,
    ], 201);
}

} 